import { AttributeValue } from "@aws-sdk/client-dynamodb";
import { marshallOptions } from "./marshall";
import { NativeAttributeValue } from "./models";
/**
 * Convert a JavaScript value to its equivalent DynamoDB AttributeValue type.
 *
 * @param data - The data to convert to a DynamoDB AttributeValue.
 * @param options - An optional configuration object for `convertToAttr`.
 */
export declare const convertToAttr: (data: NativeAttributeValue, options?: marshallOptions) => AttributeValue;
