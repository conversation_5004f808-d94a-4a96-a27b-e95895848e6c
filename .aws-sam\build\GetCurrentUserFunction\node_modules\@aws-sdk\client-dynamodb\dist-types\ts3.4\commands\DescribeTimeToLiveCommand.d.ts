import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import {
  DescribeTimeToLiveInput,
  DescribeTimeToLiveOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DescribeTimeToLiveCommandInput
  extends DescribeTimeToLiveInput {}
export interface DescribeTimeToLiveCommandOutput
  extends DescribeTimeToLiveOutput,
    __MetadataBearer {}
declare const DescribeTimeToLiveCommand_base: {
  new (
    input: DescribeTimeToLiveCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTimeToLiveCommandInput,
    DescribeTimeToLiveCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeTimeToLiveCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTimeToLiveCommandInput,
    DescribeTimeToLiveCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeTimeToLiveCommand extends DescribeTimeToLiveCommand_base {
  protected static __types: {
    api: {
      input: DescribeTimeToLiveInput;
      output: DescribeTimeToLiveOutput;
    };
    sdk: {
      input: DescribeTimeToLiveCommandInput;
      output: DescribeTimeToLiveCommandOutput;
    };
  };
}
