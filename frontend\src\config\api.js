// API Configuration
const config = {
  development: {
    apiBaseUrl: 'https://your-api-gateway-url.execute-api.us-east-1.amazonaws.com/dev',
  },
  staging: {
    apiBaseUrl: 'https://your-api-gateway-url.execute-api.us-east-1.amazonaws.com/staging',
  },
  production: {
    apiBaseUrl: 'https://your-api-gateway-url.execute-api.us-east-1.amazonaws.com/prod',
  }
}

// Determine environment
const getEnvironment = () => {
  const hostname = window.location.hostname
  
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'development'
  } else if (hostname.includes('staging')) {
    return 'staging'
  } else {
    return 'production'
  }
}

const environment = getEnvironment()
const apiConfig = config[environment]

export const API_BASE_URL = apiConfig.apiBaseUrl

// API Endpoints
export const API_ENDPOINTS = {
  REGISTER: '/users/register',
  LOGIN: '/users/login',
  CURRENT_USER: '/users/me',
}

// Default headers
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
}

// Request timeout (in milliseconds)
export const REQUEST_TIMEOUT = 10000
