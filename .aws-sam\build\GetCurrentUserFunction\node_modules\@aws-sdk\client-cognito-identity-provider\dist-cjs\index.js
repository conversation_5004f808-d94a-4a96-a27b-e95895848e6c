"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  AccountTakeoverEventActionType: () => AccountTakeoverEventActionType,
  AddCustomAttributesCommand: () => AddCustomAttributesCommand,
  AdminAddUserToGroupCommand: () => AdminAddUserToGroupCommand,
  AdminAddUserToGroupRequestFilterSensitiveLog: () => AdminAddUserToGroupRequestFilterSensitiveLog,
  AdminConfirmSignUpCommand: () => AdminConfirmSignUpCommand,
  AdminConfirmSignUpRequestFilterSensitiveLog: () => AdminConfirmSignUpRequestFilterSensitiveLog,
  AdminCreateUserCommand: () => AdminCreateUserCommand,
  AdminCreateUserRequestFilterSensitiveLog: () => AdminCreateUserRequestFilterSensitiveLog,
  AdminCreateUserResponseFilterSensitiveLog: () => AdminCreateUserResponseFilterSensitiveLog,
  AdminDeleteUserAttributesCommand: () => AdminDeleteUserAttributesCommand,
  AdminDeleteUserAttributesRequestFilterSensitiveLog: () => AdminDeleteUserAttributesRequestFilterSensitiveLog,
  AdminDeleteUserCommand: () => AdminDeleteUserCommand,
  AdminDeleteUserRequestFilterSensitiveLog: () => AdminDeleteUserRequestFilterSensitiveLog,
  AdminDisableProviderForUserCommand: () => AdminDisableProviderForUserCommand,
  AdminDisableUserCommand: () => AdminDisableUserCommand,
  AdminDisableUserRequestFilterSensitiveLog: () => AdminDisableUserRequestFilterSensitiveLog,
  AdminEnableUserCommand: () => AdminEnableUserCommand,
  AdminEnableUserRequestFilterSensitiveLog: () => AdminEnableUserRequestFilterSensitiveLog,
  AdminForgetDeviceCommand: () => AdminForgetDeviceCommand,
  AdminForgetDeviceRequestFilterSensitiveLog: () => AdminForgetDeviceRequestFilterSensitiveLog,
  AdminGetDeviceCommand: () => AdminGetDeviceCommand,
  AdminGetDeviceRequestFilterSensitiveLog: () => AdminGetDeviceRequestFilterSensitiveLog,
  AdminGetDeviceResponseFilterSensitiveLog: () => AdminGetDeviceResponseFilterSensitiveLog,
  AdminGetUserCommand: () => AdminGetUserCommand,
  AdminGetUserRequestFilterSensitiveLog: () => AdminGetUserRequestFilterSensitiveLog,
  AdminGetUserResponseFilterSensitiveLog: () => AdminGetUserResponseFilterSensitiveLog,
  AdminInitiateAuthCommand: () => AdminInitiateAuthCommand,
  AdminInitiateAuthRequestFilterSensitiveLog: () => AdminInitiateAuthRequestFilterSensitiveLog,
  AdminInitiateAuthResponseFilterSensitiveLog: () => AdminInitiateAuthResponseFilterSensitiveLog,
  AdminLinkProviderForUserCommand: () => AdminLinkProviderForUserCommand,
  AdminListDevicesCommand: () => AdminListDevicesCommand,
  AdminListDevicesRequestFilterSensitiveLog: () => AdminListDevicesRequestFilterSensitiveLog,
  AdminListDevicesResponseFilterSensitiveLog: () => AdminListDevicesResponseFilterSensitiveLog,
  AdminListGroupsForUserCommand: () => AdminListGroupsForUserCommand,
  AdminListGroupsForUserRequestFilterSensitiveLog: () => AdminListGroupsForUserRequestFilterSensitiveLog,
  AdminListUserAuthEventsCommand: () => AdminListUserAuthEventsCommand,
  AdminListUserAuthEventsRequestFilterSensitiveLog: () => AdminListUserAuthEventsRequestFilterSensitiveLog,
  AdminRemoveUserFromGroupCommand: () => AdminRemoveUserFromGroupCommand,
  AdminRemoveUserFromGroupRequestFilterSensitiveLog: () => AdminRemoveUserFromGroupRequestFilterSensitiveLog,
  AdminResetUserPasswordCommand: () => AdminResetUserPasswordCommand,
  AdminResetUserPasswordRequestFilterSensitiveLog: () => AdminResetUserPasswordRequestFilterSensitiveLog,
  AdminRespondToAuthChallengeCommand: () => AdminRespondToAuthChallengeCommand,
  AdminRespondToAuthChallengeRequestFilterSensitiveLog: () => AdminRespondToAuthChallengeRequestFilterSensitiveLog,
  AdminRespondToAuthChallengeResponseFilterSensitiveLog: () => AdminRespondToAuthChallengeResponseFilterSensitiveLog,
  AdminSetUserMFAPreferenceCommand: () => AdminSetUserMFAPreferenceCommand,
  AdminSetUserMFAPreferenceRequestFilterSensitiveLog: () => AdminSetUserMFAPreferenceRequestFilterSensitiveLog,
  AdminSetUserPasswordCommand: () => AdminSetUserPasswordCommand,
  AdminSetUserPasswordRequestFilterSensitiveLog: () => AdminSetUserPasswordRequestFilterSensitiveLog,
  AdminSetUserSettingsCommand: () => AdminSetUserSettingsCommand,
  AdminSetUserSettingsRequestFilterSensitiveLog: () => AdminSetUserSettingsRequestFilterSensitiveLog,
  AdminUpdateAuthEventFeedbackCommand: () => AdminUpdateAuthEventFeedbackCommand,
  AdminUpdateAuthEventFeedbackRequestFilterSensitiveLog: () => AdminUpdateAuthEventFeedbackRequestFilterSensitiveLog,
  AdminUpdateDeviceStatusCommand: () => AdminUpdateDeviceStatusCommand,
  AdminUpdateDeviceStatusRequestFilterSensitiveLog: () => AdminUpdateDeviceStatusRequestFilterSensitiveLog,
  AdminUpdateUserAttributesCommand: () => AdminUpdateUserAttributesCommand,
  AdminUpdateUserAttributesRequestFilterSensitiveLog: () => AdminUpdateUserAttributesRequestFilterSensitiveLog,
  AdminUserGlobalSignOutCommand: () => AdminUserGlobalSignOutCommand,
  AdminUserGlobalSignOutRequestFilterSensitiveLog: () => AdminUserGlobalSignOutRequestFilterSensitiveLog,
  AdvancedSecurityEnabledModeType: () => AdvancedSecurityEnabledModeType,
  AdvancedSecurityModeType: () => AdvancedSecurityModeType,
  AliasAttributeType: () => AliasAttributeType,
  AliasExistsException: () => AliasExistsException,
  AssetCategoryType: () => AssetCategoryType,
  AssetExtensionType: () => AssetExtensionType,
  AssociateSoftwareTokenCommand: () => AssociateSoftwareTokenCommand,
  AssociateSoftwareTokenRequestFilterSensitiveLog: () => AssociateSoftwareTokenRequestFilterSensitiveLog,
  AssociateSoftwareTokenResponseFilterSensitiveLog: () => AssociateSoftwareTokenResponseFilterSensitiveLog,
  AttributeDataType: () => AttributeDataType,
  AttributeTypeFilterSensitiveLog: () => AttributeTypeFilterSensitiveLog,
  AuthFactorType: () => AuthFactorType,
  AuthFlowType: () => AuthFlowType,
  AuthenticationResultTypeFilterSensitiveLog: () => AuthenticationResultTypeFilterSensitiveLog,
  ChallengeName: () => ChallengeName,
  ChallengeNameType: () => ChallengeNameType,
  ChallengeResponse: () => ChallengeResponse,
  ChangePasswordCommand: () => ChangePasswordCommand,
  ChangePasswordRequestFilterSensitiveLog: () => ChangePasswordRequestFilterSensitiveLog,
  CodeDeliveryFailureException: () => CodeDeliveryFailureException,
  CodeMismatchException: () => CodeMismatchException,
  CognitoIdentityProvider: () => CognitoIdentityProvider,
  CognitoIdentityProviderClient: () => CognitoIdentityProviderClient,
  CognitoIdentityProviderServiceException: () => CognitoIdentityProviderServiceException,
  ColorSchemeModeType: () => ColorSchemeModeType,
  CompleteWebAuthnRegistrationCommand: () => CompleteWebAuthnRegistrationCommand,
  CompleteWebAuthnRegistrationRequestFilterSensitiveLog: () => CompleteWebAuthnRegistrationRequestFilterSensitiveLog,
  CompromisedCredentialsEventActionType: () => CompromisedCredentialsEventActionType,
  ConcurrentModificationException: () => ConcurrentModificationException,
  ConfirmDeviceCommand: () => ConfirmDeviceCommand,
  ConfirmDeviceRequestFilterSensitiveLog: () => ConfirmDeviceRequestFilterSensitiveLog,
  ConfirmForgotPasswordCommand: () => ConfirmForgotPasswordCommand,
  ConfirmForgotPasswordRequestFilterSensitiveLog: () => ConfirmForgotPasswordRequestFilterSensitiveLog,
  ConfirmSignUpCommand: () => ConfirmSignUpCommand,
  ConfirmSignUpRequestFilterSensitiveLog: () => ConfirmSignUpRequestFilterSensitiveLog,
  ConfirmSignUpResponseFilterSensitiveLog: () => ConfirmSignUpResponseFilterSensitiveLog,
  CreateGroupCommand: () => CreateGroupCommand,
  CreateIdentityProviderCommand: () => CreateIdentityProviderCommand,
  CreateManagedLoginBrandingCommand: () => CreateManagedLoginBrandingCommand,
  CreateManagedLoginBrandingRequestFilterSensitiveLog: () => CreateManagedLoginBrandingRequestFilterSensitiveLog,
  CreateResourceServerCommand: () => CreateResourceServerCommand,
  CreateUserImportJobCommand: () => CreateUserImportJobCommand,
  CreateUserPoolClientCommand: () => CreateUserPoolClientCommand,
  CreateUserPoolClientResponseFilterSensitiveLog: () => CreateUserPoolClientResponseFilterSensitiveLog,
  CreateUserPoolCommand: () => CreateUserPoolCommand,
  CreateUserPoolDomainCommand: () => CreateUserPoolDomainCommand,
  CustomEmailSenderLambdaVersionType: () => CustomEmailSenderLambdaVersionType,
  CustomSMSSenderLambdaVersionType: () => CustomSMSSenderLambdaVersionType,
  DefaultEmailOptionType: () => DefaultEmailOptionType,
  DeleteGroupCommand: () => DeleteGroupCommand,
  DeleteIdentityProviderCommand: () => DeleteIdentityProviderCommand,
  DeleteManagedLoginBrandingCommand: () => DeleteManagedLoginBrandingCommand,
  DeleteResourceServerCommand: () => DeleteResourceServerCommand,
  DeleteUserAttributesCommand: () => DeleteUserAttributesCommand,
  DeleteUserAttributesRequestFilterSensitiveLog: () => DeleteUserAttributesRequestFilterSensitiveLog,
  DeleteUserCommand: () => DeleteUserCommand,
  DeleteUserPoolClientCommand: () => DeleteUserPoolClientCommand,
  DeleteUserPoolClientRequestFilterSensitiveLog: () => DeleteUserPoolClientRequestFilterSensitiveLog,
  DeleteUserPoolCommand: () => DeleteUserPoolCommand,
  DeleteUserPoolDomainCommand: () => DeleteUserPoolDomainCommand,
  DeleteUserRequestFilterSensitiveLog: () => DeleteUserRequestFilterSensitiveLog,
  DeleteWebAuthnCredentialCommand: () => DeleteWebAuthnCredentialCommand,
  DeleteWebAuthnCredentialRequestFilterSensitiveLog: () => DeleteWebAuthnCredentialRequestFilterSensitiveLog,
  DeletionProtectionType: () => DeletionProtectionType,
  DeliveryMediumType: () => DeliveryMediumType,
  DescribeIdentityProviderCommand: () => DescribeIdentityProviderCommand,
  DescribeManagedLoginBrandingByClientCommand: () => DescribeManagedLoginBrandingByClientCommand,
  DescribeManagedLoginBrandingByClientRequestFilterSensitiveLog: () => DescribeManagedLoginBrandingByClientRequestFilterSensitiveLog,
  DescribeManagedLoginBrandingCommand: () => DescribeManagedLoginBrandingCommand,
  DescribeResourceServerCommand: () => DescribeResourceServerCommand,
  DescribeRiskConfigurationCommand: () => DescribeRiskConfigurationCommand,
  DescribeRiskConfigurationRequestFilterSensitiveLog: () => DescribeRiskConfigurationRequestFilterSensitiveLog,
  DescribeRiskConfigurationResponseFilterSensitiveLog: () => DescribeRiskConfigurationResponseFilterSensitiveLog,
  DescribeUserImportJobCommand: () => DescribeUserImportJobCommand,
  DescribeUserPoolClientCommand: () => DescribeUserPoolClientCommand,
  DescribeUserPoolClientRequestFilterSensitiveLog: () => DescribeUserPoolClientRequestFilterSensitiveLog,
  DescribeUserPoolClientResponseFilterSensitiveLog: () => DescribeUserPoolClientResponseFilterSensitiveLog,
  DescribeUserPoolCommand: () => DescribeUserPoolCommand,
  DescribeUserPoolDomainCommand: () => DescribeUserPoolDomainCommand,
  DeviceKeyExistsException: () => DeviceKeyExistsException,
  DeviceRememberedStatusType: () => DeviceRememberedStatusType,
  DeviceTypeFilterSensitiveLog: () => DeviceTypeFilterSensitiveLog,
  DomainStatusType: () => DomainStatusType,
  DuplicateProviderException: () => DuplicateProviderException,
  EmailSendingAccountType: () => EmailSendingAccountType,
  EnableSoftwareTokenMFAException: () => EnableSoftwareTokenMFAException,
  EventFilterType: () => EventFilterType,
  EventResponseType: () => EventResponseType,
  EventSourceName: () => EventSourceName,
  EventType: () => EventType,
  ExpiredCodeException: () => ExpiredCodeException,
  ExplicitAuthFlowsType: () => ExplicitAuthFlowsType,
  FeatureType: () => FeatureType,
  FeatureUnavailableInTierException: () => FeatureUnavailableInTierException,
  FeedbackValueType: () => FeedbackValueType,
  ForbiddenException: () => ForbiddenException,
  ForgetDeviceCommand: () => ForgetDeviceCommand,
  ForgetDeviceRequestFilterSensitiveLog: () => ForgetDeviceRequestFilterSensitiveLog,
  ForgotPasswordCommand: () => ForgotPasswordCommand,
  ForgotPasswordRequestFilterSensitiveLog: () => ForgotPasswordRequestFilterSensitiveLog,
  GetCSVHeaderCommand: () => GetCSVHeaderCommand,
  GetDeviceCommand: () => GetDeviceCommand,
  GetDeviceRequestFilterSensitiveLog: () => GetDeviceRequestFilterSensitiveLog,
  GetDeviceResponseFilterSensitiveLog: () => GetDeviceResponseFilterSensitiveLog,
  GetGroupCommand: () => GetGroupCommand,
  GetIdentityProviderByIdentifierCommand: () => GetIdentityProviderByIdentifierCommand,
  GetLogDeliveryConfigurationCommand: () => GetLogDeliveryConfigurationCommand,
  GetSigningCertificateCommand: () => GetSigningCertificateCommand,
  GetTokensFromRefreshTokenCommand: () => GetTokensFromRefreshTokenCommand,
  GetTokensFromRefreshTokenRequestFilterSensitiveLog: () => GetTokensFromRefreshTokenRequestFilterSensitiveLog,
  GetTokensFromRefreshTokenResponseFilterSensitiveLog: () => GetTokensFromRefreshTokenResponseFilterSensitiveLog,
  GetUICustomizationCommand: () => GetUICustomizationCommand,
  GetUICustomizationRequestFilterSensitiveLog: () => GetUICustomizationRequestFilterSensitiveLog,
  GetUICustomizationResponseFilterSensitiveLog: () => GetUICustomizationResponseFilterSensitiveLog,
  GetUserAttributeVerificationCodeCommand: () => GetUserAttributeVerificationCodeCommand,
  GetUserAttributeVerificationCodeRequestFilterSensitiveLog: () => GetUserAttributeVerificationCodeRequestFilterSensitiveLog,
  GetUserAuthFactorsCommand: () => GetUserAuthFactorsCommand,
  GetUserAuthFactorsRequestFilterSensitiveLog: () => GetUserAuthFactorsRequestFilterSensitiveLog,
  GetUserAuthFactorsResponseFilterSensitiveLog: () => GetUserAuthFactorsResponseFilterSensitiveLog,
  GetUserCommand: () => GetUserCommand,
  GetUserPoolMfaConfigCommand: () => GetUserPoolMfaConfigCommand,
  GetUserRequestFilterSensitiveLog: () => GetUserRequestFilterSensitiveLog,
  GetUserResponseFilterSensitiveLog: () => GetUserResponseFilterSensitiveLog,
  GlobalSignOutCommand: () => GlobalSignOutCommand,
  GlobalSignOutRequestFilterSensitiveLog: () => GlobalSignOutRequestFilterSensitiveLog,
  GroupExistsException: () => GroupExistsException,
  IdentityProviderTypeType: () => IdentityProviderTypeType,
  InitiateAuthCommand: () => InitiateAuthCommand,
  InitiateAuthRequestFilterSensitiveLog: () => InitiateAuthRequestFilterSensitiveLog,
  InitiateAuthResponseFilterSensitiveLog: () => InitiateAuthResponseFilterSensitiveLog,
  InternalErrorException: () => InternalErrorException,
  InvalidEmailRoleAccessPolicyException: () => InvalidEmailRoleAccessPolicyException,
  InvalidLambdaResponseException: () => InvalidLambdaResponseException,
  InvalidOAuthFlowException: () => InvalidOAuthFlowException,
  InvalidParameterException: () => InvalidParameterException,
  InvalidPasswordException: () => InvalidPasswordException,
  InvalidSmsRoleAccessPolicyException: () => InvalidSmsRoleAccessPolicyException,
  InvalidSmsRoleTrustRelationshipException: () => InvalidSmsRoleTrustRelationshipException,
  InvalidUserPoolConfigurationException: () => InvalidUserPoolConfigurationException,
  LimitExceededException: () => LimitExceededException,
  ListDevicesCommand: () => ListDevicesCommand,
  ListDevicesRequestFilterSensitiveLog: () => ListDevicesRequestFilterSensitiveLog,
  ListDevicesResponseFilterSensitiveLog: () => ListDevicesResponseFilterSensitiveLog,
  ListGroupsCommand: () => ListGroupsCommand,
  ListIdentityProvidersCommand: () => ListIdentityProvidersCommand,
  ListResourceServersCommand: () => ListResourceServersCommand,
  ListTagsForResourceCommand: () => ListTagsForResourceCommand,
  ListUserImportJobsCommand: () => ListUserImportJobsCommand,
  ListUserPoolClientsCommand: () => ListUserPoolClientsCommand,
  ListUserPoolClientsResponseFilterSensitiveLog: () => ListUserPoolClientsResponseFilterSensitiveLog,
  ListUserPoolsCommand: () => ListUserPoolsCommand,
  ListUsersCommand: () => ListUsersCommand,
  ListUsersInGroupCommand: () => ListUsersInGroupCommand,
  ListUsersInGroupResponseFilterSensitiveLog: () => ListUsersInGroupResponseFilterSensitiveLog,
  ListUsersResponseFilterSensitiveLog: () => ListUsersResponseFilterSensitiveLog,
  ListWebAuthnCredentialsCommand: () => ListWebAuthnCredentialsCommand,
  ListWebAuthnCredentialsRequestFilterSensitiveLog: () => ListWebAuthnCredentialsRequestFilterSensitiveLog,
  LogLevel: () => LogLevel,
  MFAMethodNotFoundException: () => MFAMethodNotFoundException,
  ManagedLoginBrandingExistsException: () => ManagedLoginBrandingExistsException,
  MessageActionType: () => MessageActionType,
  NotAuthorizedException: () => NotAuthorizedException,
  OAuthFlowType: () => OAuthFlowType,
  PasswordHistoryPolicyViolationException: () => PasswordHistoryPolicyViolationException,
  PasswordResetRequiredException: () => PasswordResetRequiredException,
  PreTokenGenerationLambdaVersionType: () => PreTokenGenerationLambdaVersionType,
  PreconditionNotMetException: () => PreconditionNotMetException,
  PreventUserExistenceErrorTypes: () => PreventUserExistenceErrorTypes,
  RecoveryOptionNameType: () => RecoveryOptionNameType,
  RefreshTokenReuseException: () => RefreshTokenReuseException,
  ResendConfirmationCodeCommand: () => ResendConfirmationCodeCommand,
  ResendConfirmationCodeRequestFilterSensitiveLog: () => ResendConfirmationCodeRequestFilterSensitiveLog,
  ResourceNotFoundException: () => ResourceNotFoundException,
  RespondToAuthChallengeCommand: () => RespondToAuthChallengeCommand,
  RespondToAuthChallengeRequestFilterSensitiveLog: () => RespondToAuthChallengeRequestFilterSensitiveLog,
  RespondToAuthChallengeResponseFilterSensitiveLog: () => RespondToAuthChallengeResponseFilterSensitiveLog,
  RevokeTokenCommand: () => RevokeTokenCommand,
  RevokeTokenRequestFilterSensitiveLog: () => RevokeTokenRequestFilterSensitiveLog,
  RiskConfigurationTypeFilterSensitiveLog: () => RiskConfigurationTypeFilterSensitiveLog,
  RiskDecisionType: () => RiskDecisionType,
  RiskLevelType: () => RiskLevelType,
  ScopeDoesNotExistException: () => ScopeDoesNotExistException,
  SetLogDeliveryConfigurationCommand: () => SetLogDeliveryConfigurationCommand,
  SetRiskConfigurationCommand: () => SetRiskConfigurationCommand,
  SetRiskConfigurationRequestFilterSensitiveLog: () => SetRiskConfigurationRequestFilterSensitiveLog,
  SetRiskConfigurationResponseFilterSensitiveLog: () => SetRiskConfigurationResponseFilterSensitiveLog,
  SetUICustomizationCommand: () => SetUICustomizationCommand,
  SetUICustomizationRequestFilterSensitiveLog: () => SetUICustomizationRequestFilterSensitiveLog,
  SetUICustomizationResponseFilterSensitiveLog: () => SetUICustomizationResponseFilterSensitiveLog,
  SetUserMFAPreferenceCommand: () => SetUserMFAPreferenceCommand,
  SetUserMFAPreferenceRequestFilterSensitiveLog: () => SetUserMFAPreferenceRequestFilterSensitiveLog,
  SetUserPoolMfaConfigCommand: () => SetUserPoolMfaConfigCommand,
  SetUserSettingsCommand: () => SetUserSettingsCommand,
  SetUserSettingsRequestFilterSensitiveLog: () => SetUserSettingsRequestFilterSensitiveLog,
  SignUpCommand: () => SignUpCommand,
  SignUpRequestFilterSensitiveLog: () => SignUpRequestFilterSensitiveLog,
  SignUpResponseFilterSensitiveLog: () => SignUpResponseFilterSensitiveLog,
  SoftwareTokenMFANotFoundException: () => SoftwareTokenMFANotFoundException,
  StartUserImportJobCommand: () => StartUserImportJobCommand,
  StartWebAuthnRegistrationCommand: () => StartWebAuthnRegistrationCommand,
  StartWebAuthnRegistrationRequestFilterSensitiveLog: () => StartWebAuthnRegistrationRequestFilterSensitiveLog,
  StatusType: () => StatusType,
  StopUserImportJobCommand: () => StopUserImportJobCommand,
  TagResourceCommand: () => TagResourceCommand,
  TierChangeNotAllowedException: () => TierChangeNotAllowedException,
  TimeUnitsType: () => TimeUnitsType,
  TooManyFailedAttemptsException: () => TooManyFailedAttemptsException,
  TooManyRequestsException: () => TooManyRequestsException,
  UICustomizationTypeFilterSensitiveLog: () => UICustomizationTypeFilterSensitiveLog,
  UnauthorizedException: () => UnauthorizedException,
  UnexpectedLambdaException: () => UnexpectedLambdaException,
  UnsupportedIdentityProviderException: () => UnsupportedIdentityProviderException,
  UnsupportedOperationException: () => UnsupportedOperationException,
  UnsupportedTokenTypeException: () => UnsupportedTokenTypeException,
  UnsupportedUserStateException: () => UnsupportedUserStateException,
  UntagResourceCommand: () => UntagResourceCommand,
  UpdateAuthEventFeedbackCommand: () => UpdateAuthEventFeedbackCommand,
  UpdateAuthEventFeedbackRequestFilterSensitiveLog: () => UpdateAuthEventFeedbackRequestFilterSensitiveLog,
  UpdateDeviceStatusCommand: () => UpdateDeviceStatusCommand,
  UpdateDeviceStatusRequestFilterSensitiveLog: () => UpdateDeviceStatusRequestFilterSensitiveLog,
  UpdateGroupCommand: () => UpdateGroupCommand,
  UpdateIdentityProviderCommand: () => UpdateIdentityProviderCommand,
  UpdateManagedLoginBrandingCommand: () => UpdateManagedLoginBrandingCommand,
  UpdateResourceServerCommand: () => UpdateResourceServerCommand,
  UpdateUserAttributesCommand: () => UpdateUserAttributesCommand,
  UpdateUserAttributesRequestFilterSensitiveLog: () => UpdateUserAttributesRequestFilterSensitiveLog,
  UpdateUserPoolClientCommand: () => UpdateUserPoolClientCommand,
  UpdateUserPoolClientRequestFilterSensitiveLog: () => UpdateUserPoolClientRequestFilterSensitiveLog,
  UpdateUserPoolClientResponseFilterSensitiveLog: () => UpdateUserPoolClientResponseFilterSensitiveLog,
  UpdateUserPoolCommand: () => UpdateUserPoolCommand,
  UpdateUserPoolDomainCommand: () => UpdateUserPoolDomainCommand,
  UserContextDataTypeFilterSensitiveLog: () => UserContextDataTypeFilterSensitiveLog,
  UserImportInProgressException: () => UserImportInProgressException,
  UserImportJobStatusType: () => UserImportJobStatusType,
  UserLambdaValidationException: () => UserLambdaValidationException,
  UserNotConfirmedException: () => UserNotConfirmedException,
  UserNotFoundException: () => UserNotFoundException,
  UserPoolAddOnNotEnabledException: () => UserPoolAddOnNotEnabledException,
  UserPoolClientDescriptionFilterSensitiveLog: () => UserPoolClientDescriptionFilterSensitiveLog,
  UserPoolClientTypeFilterSensitiveLog: () => UserPoolClientTypeFilterSensitiveLog,
  UserPoolMfaType: () => UserPoolMfaType,
  UserPoolTaggingException: () => UserPoolTaggingException,
  UserPoolTierType: () => UserPoolTierType,
  UserStatusType: () => UserStatusType,
  UserTypeFilterSensitiveLog: () => UserTypeFilterSensitiveLog,
  UserVerificationType: () => UserVerificationType,
  UsernameAttributeType: () => UsernameAttributeType,
  UsernameExistsException: () => UsernameExistsException,
  VerifiedAttributeType: () => VerifiedAttributeType,
  VerifySoftwareTokenCommand: () => VerifySoftwareTokenCommand,
  VerifySoftwareTokenRequestFilterSensitiveLog: () => VerifySoftwareTokenRequestFilterSensitiveLog,
  VerifySoftwareTokenResponseFilterSensitiveLog: () => VerifySoftwareTokenResponseFilterSensitiveLog,
  VerifySoftwareTokenResponseType: () => VerifySoftwareTokenResponseType,
  VerifyUserAttributeCommand: () => VerifyUserAttributeCommand,
  VerifyUserAttributeRequestFilterSensitiveLog: () => VerifyUserAttributeRequestFilterSensitiveLog,
  WebAuthnChallengeNotFoundException: () => WebAuthnChallengeNotFoundException,
  WebAuthnClientMismatchException: () => WebAuthnClientMismatchException,
  WebAuthnConfigurationMissingException: () => WebAuthnConfigurationMissingException,
  WebAuthnCredentialNotSupportedException: () => WebAuthnCredentialNotSupportedException,
  WebAuthnNotEnabledException: () => WebAuthnNotEnabledException,
  WebAuthnOriginNotAllowedException: () => WebAuthnOriginNotAllowedException,
  WebAuthnRelyingPartyMismatchException: () => WebAuthnRelyingPartyMismatchException,
  __Client: () => import_smithy_client.Client,
  paginateAdminListGroupsForUser: () => paginateAdminListGroupsForUser,
  paginateAdminListUserAuthEvents: () => paginateAdminListUserAuthEvents,
  paginateListGroups: () => paginateListGroups,
  paginateListIdentityProviders: () => paginateListIdentityProviders,
  paginateListResourceServers: () => paginateListResourceServers,
  paginateListUserPoolClients: () => paginateListUserPoolClients,
  paginateListUserPools: () => paginateListUserPools,
  paginateListUsers: () => paginateListUsers,
  paginateListUsersInGroup: () => paginateListUsersInGroup
});
module.exports = __toCommonJS(index_exports);

// src/CognitoIdentityProviderClient.ts
var import_middleware_host_header = require("@aws-sdk/middleware-host-header");
var import_middleware_logger = require("@aws-sdk/middleware-logger");
var import_middleware_recursion_detection = require("@aws-sdk/middleware-recursion-detection");
var import_middleware_user_agent = require("@aws-sdk/middleware-user-agent");
var import_config_resolver = require("@smithy/config-resolver");
var import_core = require("@smithy/core");
var import_middleware_content_length = require("@smithy/middleware-content-length");
var import_middleware_endpoint = require("@smithy/middleware-endpoint");
var import_middleware_retry = require("@smithy/middleware-retry");

var import_httpAuthSchemeProvider = require("./auth/httpAuthSchemeProvider");

// src/endpoint/EndpointParameters.ts
var resolveClientEndpointParameters = /* @__PURE__ */ __name((options) => {
  return Object.assign(options, {
    useDualstackEndpoint: options.useDualstackEndpoint ?? false,
    useFipsEndpoint: options.useFipsEndpoint ?? false,
    defaultSigningName: "cognito-idp"
  });
}, "resolveClientEndpointParameters");
var commonParams = {
  UseFIPS: { type: "builtInParams", name: "useFipsEndpoint" },
  Endpoint: { type: "builtInParams", name: "endpoint" },
  Region: { type: "builtInParams", name: "region" },
  UseDualStack: { type: "builtInParams", name: "useDualstackEndpoint" }
};

// src/CognitoIdentityProviderClient.ts
var import_runtimeConfig = require("././runtimeConfig");

// src/runtimeExtensions.ts
var import_region_config_resolver = require("@aws-sdk/region-config-resolver");
var import_protocol_http = require("@smithy/protocol-http");
var import_smithy_client = require("@smithy/smithy-client");

// src/auth/httpAuthExtensionConfiguration.ts
var getHttpAuthExtensionConfiguration = /* @__PURE__ */ __name((runtimeConfig) => {
  const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;
  let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;
  let _credentials = runtimeConfig.credentials;
  return {
    setHttpAuthScheme(httpAuthScheme) {
      const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);
      if (index === -1) {
        _httpAuthSchemes.push(httpAuthScheme);
      } else {
        _httpAuthSchemes.splice(index, 1, httpAuthScheme);
      }
    },
    httpAuthSchemes() {
      return _httpAuthSchemes;
    },
    setHttpAuthSchemeProvider(httpAuthSchemeProvider) {
      _httpAuthSchemeProvider = httpAuthSchemeProvider;
    },
    httpAuthSchemeProvider() {
      return _httpAuthSchemeProvider;
    },
    setCredentials(credentials) {
      _credentials = credentials;
    },
    credentials() {
      return _credentials;
    }
  };
}, "getHttpAuthExtensionConfiguration");
var resolveHttpAuthRuntimeConfig = /* @__PURE__ */ __name((config) => {
  return {
    httpAuthSchemes: config.httpAuthSchemes(),
    httpAuthSchemeProvider: config.httpAuthSchemeProvider(),
    credentials: config.credentials()
  };
}, "resolveHttpAuthRuntimeConfig");

// src/runtimeExtensions.ts
var resolveRuntimeExtensions = /* @__PURE__ */ __name((runtimeConfig, extensions) => {
  const extensionConfiguration = Object.assign(
    (0, import_region_config_resolver.getAwsRegionExtensionConfiguration)(runtimeConfig),
    (0, import_smithy_client.getDefaultExtensionConfiguration)(runtimeConfig),
    (0, import_protocol_http.getHttpHandlerExtensionConfiguration)(runtimeConfig),
    getHttpAuthExtensionConfiguration(runtimeConfig)
  );
  extensions.forEach((extension) => extension.configure(extensionConfiguration));
  return Object.assign(
    runtimeConfig,
    (0, import_region_config_resolver.resolveAwsRegionExtensionConfiguration)(extensionConfiguration),
    (0, import_smithy_client.resolveDefaultRuntimeConfig)(extensionConfiguration),
    (0, import_protocol_http.resolveHttpHandlerRuntimeConfig)(extensionConfiguration),
    resolveHttpAuthRuntimeConfig(extensionConfiguration)
  );
}, "resolveRuntimeExtensions");

// src/CognitoIdentityProviderClient.ts
var CognitoIdentityProviderClient = class extends import_smithy_client.Client {
  static {
    __name(this, "CognitoIdentityProviderClient");
  }
  /**
   * The resolved configuration of CognitoIdentityProviderClient class. This is resolved and normalized from the {@link CognitoIdentityProviderClientConfig | constructor configuration interface}.
   */
  config;
  constructor(...[configuration]) {
    const _config_0 = (0, import_runtimeConfig.getRuntimeConfig)(configuration || {});
    super(_config_0);
    this.initConfig = _config_0;
    const _config_1 = resolveClientEndpointParameters(_config_0);
    const _config_2 = (0, import_middleware_user_agent.resolveUserAgentConfig)(_config_1);
    const _config_3 = (0, import_middleware_retry.resolveRetryConfig)(_config_2);
    const _config_4 = (0, import_config_resolver.resolveRegionConfig)(_config_3);
    const _config_5 = (0, import_middleware_host_header.resolveHostHeaderConfig)(_config_4);
    const _config_6 = (0, import_middleware_endpoint.resolveEndpointConfig)(_config_5);
    const _config_7 = (0, import_httpAuthSchemeProvider.resolveHttpAuthSchemeConfig)(_config_6);
    const _config_8 = resolveRuntimeExtensions(_config_7, configuration?.extensions || []);
    this.config = _config_8;
    this.middlewareStack.use((0, import_middleware_user_agent.getUserAgentPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_retry.getRetryPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_content_length.getContentLengthPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_host_header.getHostHeaderPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_logger.getLoggerPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_recursion_detection.getRecursionDetectionPlugin)(this.config));
    this.middlewareStack.use(
      (0, import_core.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {
        httpAuthSchemeParametersProvider: import_httpAuthSchemeProvider.defaultCognitoIdentityProviderHttpAuthSchemeParametersProvider,
        identityProviderConfigProvider: /* @__PURE__ */ __name(async (config) => new import_core.DefaultIdentityProviderConfig({
          "aws.auth#sigv4": config.credentials
        }), "identityProviderConfigProvider")
      })
    );
    this.middlewareStack.use((0, import_core.getHttpSigningPlugin)(this.config));
  }
  /**
   * Destroy underlying resources, like sockets. It's usually not necessary to do this.
   * However in Node.js, it's best to explicitly shut down the client's agent when it is no longer needed.
   * Otherwise, sockets might stay open for quite a long time before the server terminates them.
   */
  destroy() {
    super.destroy();
  }
};

// src/CognitoIdentityProvider.ts


// src/commands/AddCustomAttributesCommand.ts

var import_middleware_serde = require("@smithy/middleware-serde");


// src/protocols/Aws_json1_1.ts
var import_core2 = require("@aws-sdk/core");



// src/models/CognitoIdentityProviderServiceException.ts

var CognitoIdentityProviderServiceException = class _CognitoIdentityProviderServiceException extends import_smithy_client.ServiceException {
  static {
    __name(this, "CognitoIdentityProviderServiceException");
  }
  /**
   * @internal
   */
  constructor(options) {
    super(options);
    Object.setPrototypeOf(this, _CognitoIdentityProviderServiceException.prototype);
  }
};

// src/models/models_0.ts

var RecoveryOptionNameType = {
  ADMIN_ONLY: "admin_only",
  VERIFIED_EMAIL: "verified_email",
  VERIFIED_PHONE_NUMBER: "verified_phone_number"
};
var AccountTakeoverEventActionType = {
  BLOCK: "BLOCK",
  MFA_IF_CONFIGURED: "MFA_IF_CONFIGURED",
  MFA_REQUIRED: "MFA_REQUIRED",
  NO_ACTION: "NO_ACTION"
};
var AttributeDataType = {
  BOOLEAN: "Boolean",
  DATETIME: "DateTime",
  NUMBER: "Number",
  STRING: "String"
};
var InternalErrorException = class _InternalErrorException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "InternalErrorException");
  }
  name = "InternalErrorException";
  $fault = "server";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InternalErrorException",
      $fault: "server",
      ...opts
    });
    Object.setPrototypeOf(this, _InternalErrorException.prototype);
  }
};
var InvalidParameterException = class _InvalidParameterException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "InvalidParameterException");
  }
  name = "InvalidParameterException";
  $fault = "client";
  /**
   * <p>The reason code of the exception.</p>
   * @public
   */
  reasonCode;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidParameterException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidParameterException.prototype);
    this.reasonCode = opts.reasonCode;
  }
};
var NotAuthorizedException = class _NotAuthorizedException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "NotAuthorizedException");
  }
  name = "NotAuthorizedException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "NotAuthorizedException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _NotAuthorizedException.prototype);
  }
};
var ResourceNotFoundException = class _ResourceNotFoundException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "ResourceNotFoundException");
  }
  name = "ResourceNotFoundException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ResourceNotFoundException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ResourceNotFoundException.prototype);
  }
};
var TooManyRequestsException = class _TooManyRequestsException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "TooManyRequestsException");
  }
  name = "TooManyRequestsException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "TooManyRequestsException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _TooManyRequestsException.prototype);
  }
};
var UserImportInProgressException = class _UserImportInProgressException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UserImportInProgressException");
  }
  name = "UserImportInProgressException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UserImportInProgressException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UserImportInProgressException.prototype);
  }
};
var UserNotFoundException = class _UserNotFoundException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UserNotFoundException");
  }
  name = "UserNotFoundException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UserNotFoundException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UserNotFoundException.prototype);
  }
};
var InvalidLambdaResponseException = class _InvalidLambdaResponseException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "InvalidLambdaResponseException");
  }
  name = "InvalidLambdaResponseException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidLambdaResponseException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidLambdaResponseException.prototype);
  }
};
var LimitExceededException = class _LimitExceededException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "LimitExceededException");
  }
  name = "LimitExceededException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "LimitExceededException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _LimitExceededException.prototype);
  }
};
var TooManyFailedAttemptsException = class _TooManyFailedAttemptsException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "TooManyFailedAttemptsException");
  }
  name = "TooManyFailedAttemptsException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "TooManyFailedAttemptsException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _TooManyFailedAttemptsException.prototype);
  }
};
var UnexpectedLambdaException = class _UnexpectedLambdaException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UnexpectedLambdaException");
  }
  name = "UnexpectedLambdaException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UnexpectedLambdaException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UnexpectedLambdaException.prototype);
  }
};
var UserLambdaValidationException = class _UserLambdaValidationException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UserLambdaValidationException");
  }
  name = "UserLambdaValidationException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UserLambdaValidationException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UserLambdaValidationException.prototype);
  }
};
var DeliveryMediumType = {
  EMAIL: "EMAIL",
  SMS: "SMS"
};
var MessageActionType = {
  RESEND: "RESEND",
  SUPPRESS: "SUPPRESS"
};
var UserStatusType = {
  ARCHIVED: "ARCHIVED",
  COMPROMISED: "COMPROMISED",
  CONFIRMED: "CONFIRMED",
  EXTERNAL_PROVIDER: "EXTERNAL_PROVIDER",
  FORCE_CHANGE_PASSWORD: "FORCE_CHANGE_PASSWORD",
  RESET_REQUIRED: "RESET_REQUIRED",
  UNCONFIRMED: "UNCONFIRMED",
  UNKNOWN: "UNKNOWN"
};
var CodeDeliveryFailureException = class _CodeDeliveryFailureException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "CodeDeliveryFailureException");
  }
  name = "CodeDeliveryFailureException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "CodeDeliveryFailureException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _CodeDeliveryFailureException.prototype);
  }
};
var InvalidPasswordException = class _InvalidPasswordException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "InvalidPasswordException");
  }
  name = "InvalidPasswordException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidPasswordException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidPasswordException.prototype);
  }
};
var InvalidSmsRoleAccessPolicyException = class _InvalidSmsRoleAccessPolicyException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "InvalidSmsRoleAccessPolicyException");
  }
  name = "InvalidSmsRoleAccessPolicyException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidSmsRoleAccessPolicyException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidSmsRoleAccessPolicyException.prototype);
  }
};
var InvalidSmsRoleTrustRelationshipException = class _InvalidSmsRoleTrustRelationshipException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "InvalidSmsRoleTrustRelationshipException");
  }
  name = "InvalidSmsRoleTrustRelationshipException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidSmsRoleTrustRelationshipException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidSmsRoleTrustRelationshipException.prototype);
  }
};
var PreconditionNotMetException = class _PreconditionNotMetException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "PreconditionNotMetException");
  }
  name = "PreconditionNotMetException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "PreconditionNotMetException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _PreconditionNotMetException.prototype);
  }
};
var UnsupportedUserStateException = class _UnsupportedUserStateException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UnsupportedUserStateException");
  }
  name = "UnsupportedUserStateException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UnsupportedUserStateException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UnsupportedUserStateException.prototype);
  }
};
var UsernameExistsException = class _UsernameExistsException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UsernameExistsException");
  }
  name = "UsernameExistsException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UsernameExistsException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UsernameExistsException.prototype);
  }
};
var AliasExistsException = class _AliasExistsException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "AliasExistsException");
  }
  name = "AliasExistsException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "AliasExistsException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _AliasExistsException.prototype);
  }
};
var InvalidUserPoolConfigurationException = class _InvalidUserPoolConfigurationException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "InvalidUserPoolConfigurationException");
  }
  name = "InvalidUserPoolConfigurationException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidUserPoolConfigurationException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidUserPoolConfigurationException.prototype);
  }
};
var AuthFlowType = {
  ADMIN_NO_SRP_AUTH: "ADMIN_NO_SRP_AUTH",
  ADMIN_USER_PASSWORD_AUTH: "ADMIN_USER_PASSWORD_AUTH",
  CUSTOM_AUTH: "CUSTOM_AUTH",
  REFRESH_TOKEN: "REFRESH_TOKEN",
  REFRESH_TOKEN_AUTH: "REFRESH_TOKEN_AUTH",
  USER_AUTH: "USER_AUTH",
  USER_PASSWORD_AUTH: "USER_PASSWORD_AUTH",
  USER_SRP_AUTH: "USER_SRP_AUTH"
};
var ChallengeNameType = {
  ADMIN_NO_SRP_AUTH: "ADMIN_NO_SRP_AUTH",
  CUSTOM_CHALLENGE: "CUSTOM_CHALLENGE",
  DEVICE_PASSWORD_VERIFIER: "DEVICE_PASSWORD_VERIFIER",
  DEVICE_SRP_AUTH: "DEVICE_SRP_AUTH",
  EMAIL_OTP: "EMAIL_OTP",
  MFA_SETUP: "MFA_SETUP",
  NEW_PASSWORD_REQUIRED: "NEW_PASSWORD_REQUIRED",
  PASSWORD: "PASSWORD",
  PASSWORD_SRP: "PASSWORD_SRP",
  PASSWORD_VERIFIER: "PASSWORD_VERIFIER",
  SELECT_CHALLENGE: "SELECT_CHALLENGE",
  SELECT_MFA_TYPE: "SELECT_MFA_TYPE",
  SMS_MFA: "SMS_MFA",
  SMS_OTP: "SMS_OTP",
  SOFTWARE_TOKEN_MFA: "SOFTWARE_TOKEN_MFA",
  WEB_AUTHN: "WEB_AUTHN"
};
var InvalidEmailRoleAccessPolicyException = class _InvalidEmailRoleAccessPolicyException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "InvalidEmailRoleAccessPolicyException");
  }
  name = "InvalidEmailRoleAccessPolicyException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidEmailRoleAccessPolicyException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidEmailRoleAccessPolicyException.prototype);
  }
};
var MFAMethodNotFoundException = class _MFAMethodNotFoundException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "MFAMethodNotFoundException");
  }
  name = "MFAMethodNotFoundException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "MFAMethodNotFoundException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _MFAMethodNotFoundException.prototype);
  }
};
var PasswordResetRequiredException = class _PasswordResetRequiredException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "PasswordResetRequiredException");
  }
  name = "PasswordResetRequiredException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "PasswordResetRequiredException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _PasswordResetRequiredException.prototype);
  }
};
var UnsupportedOperationException = class _UnsupportedOperationException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UnsupportedOperationException");
  }
  name = "UnsupportedOperationException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UnsupportedOperationException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UnsupportedOperationException.prototype);
  }
};
var UserNotConfirmedException = class _UserNotConfirmedException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UserNotConfirmedException");
  }
  name = "UserNotConfirmedException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UserNotConfirmedException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UserNotConfirmedException.prototype);
  }
};
var ChallengeName = {
  Mfa: "Mfa",
  Password: "Password"
};
var ChallengeResponse = {
  Failure: "Failure",
  Success: "Success"
};
var FeedbackValueType = {
  INVALID: "Invalid",
  VALID: "Valid"
};
var EventResponseType = {
  Fail: "Fail",
  InProgress: "InProgress",
  Pass: "Pass"
};
var RiskDecisionType = {
  AccountTakeover: "AccountTakeover",
  Block: "Block",
  NoRisk: "NoRisk"
};
var RiskLevelType = {
  High: "High",
  Low: "Low",
  Medium: "Medium"
};
var EventType = {
  ForgotPassword: "ForgotPassword",
  PasswordChange: "PasswordChange",
  ResendCode: "ResendCode",
  SignIn: "SignIn",
  SignUp: "SignUp"
};
var UserPoolAddOnNotEnabledException = class _UserPoolAddOnNotEnabledException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UserPoolAddOnNotEnabledException");
  }
  name = "UserPoolAddOnNotEnabledException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UserPoolAddOnNotEnabledException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UserPoolAddOnNotEnabledException.prototype);
  }
};
var CodeMismatchException = class _CodeMismatchException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "CodeMismatchException");
  }
  name = "CodeMismatchException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "CodeMismatchException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _CodeMismatchException.prototype);
  }
};
var ExpiredCodeException = class _ExpiredCodeException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "ExpiredCodeException");
  }
  name = "ExpiredCodeException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ExpiredCodeException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ExpiredCodeException.prototype);
  }
};
var PasswordHistoryPolicyViolationException = class _PasswordHistoryPolicyViolationException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "PasswordHistoryPolicyViolationException");
  }
  name = "PasswordHistoryPolicyViolationException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "PasswordHistoryPolicyViolationException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _PasswordHistoryPolicyViolationException.prototype);
  }
};
var SoftwareTokenMFANotFoundException = class _SoftwareTokenMFANotFoundException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "SoftwareTokenMFANotFoundException");
  }
  name = "SoftwareTokenMFANotFoundException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "SoftwareTokenMFANotFoundException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _SoftwareTokenMFANotFoundException.prototype);
  }
};
var DeviceRememberedStatusType = {
  NOT_REMEMBERED: "not_remembered",
  REMEMBERED: "remembered"
};
var AdvancedSecurityEnabledModeType = {
  AUDIT: "AUDIT",
  ENFORCED: "ENFORCED"
};
var AdvancedSecurityModeType = {
  AUDIT: "AUDIT",
  ENFORCED: "ENFORCED",
  OFF: "OFF"
};
var AliasAttributeType = {
  EMAIL: "email",
  PHONE_NUMBER: "phone_number",
  PREFERRED_USERNAME: "preferred_username"
};
var AuthFactorType = {
  EMAIL_OTP: "EMAIL_OTP",
  PASSWORD: "PASSWORD",
  SMS_OTP: "SMS_OTP",
  WEB_AUTHN: "WEB_AUTHN"
};
var AssetCategoryType = {
  AUTH_APP_GRAPHIC: "AUTH_APP_GRAPHIC",
  EMAIL_GRAPHIC: "EMAIL_GRAPHIC",
  FAVICON_ICO: "FAVICON_ICO",
  FAVICON_SVG: "FAVICON_SVG",
  FORM_BACKGROUND: "FORM_BACKGROUND",
  FORM_LOGO: "FORM_LOGO",
  IDP_BUTTON_ICON: "IDP_BUTTON_ICON",
  PAGE_BACKGROUND: "PAGE_BACKGROUND",
  PAGE_FOOTER_BACKGROUND: "PAGE_FOOTER_BACKGROUND",
  PAGE_FOOTER_LOGO: "PAGE_FOOTER_LOGO",
  PAGE_HEADER_BACKGROUND: "PAGE_HEADER_BACKGROUND",
  PAGE_HEADER_LOGO: "PAGE_HEADER_LOGO",
  PASSKEY_GRAPHIC: "PASSKEY_GRAPHIC",
  PASSWORD_GRAPHIC: "PASSWORD_GRAPHIC",
  SMS_GRAPHIC: "SMS_GRAPHIC"
};
var AssetExtensionType = {
  ICO: "ICO",
  JPEG: "JPEG",
  PNG: "PNG",
  SVG: "SVG",
  WEBP: "WEBP"
};
var ColorSchemeModeType = {
  DARK: "DARK",
  DYNAMIC: "DYNAMIC",
  LIGHT: "LIGHT"
};
var ConcurrentModificationException = class _ConcurrentModificationException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "ConcurrentModificationException");
  }
  name = "ConcurrentModificationException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ConcurrentModificationException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ConcurrentModificationException.prototype);
  }
};
var ForbiddenException = class _ForbiddenException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "ForbiddenException");
  }
  name = "ForbiddenException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ForbiddenException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ForbiddenException.prototype);
  }
};
var VerifiedAttributeType = {
  EMAIL: "email",
  PHONE_NUMBER: "phone_number"
};
var WebAuthnChallengeNotFoundException = class _WebAuthnChallengeNotFoundException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "WebAuthnChallengeNotFoundException");
  }
  name = "WebAuthnChallengeNotFoundException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "WebAuthnChallengeNotFoundException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _WebAuthnChallengeNotFoundException.prototype);
  }
};
var WebAuthnClientMismatchException = class _WebAuthnClientMismatchException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "WebAuthnClientMismatchException");
  }
  name = "WebAuthnClientMismatchException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "WebAuthnClientMismatchException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _WebAuthnClientMismatchException.prototype);
  }
};
var WebAuthnCredentialNotSupportedException = class _WebAuthnCredentialNotSupportedException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "WebAuthnCredentialNotSupportedException");
  }
  name = "WebAuthnCredentialNotSupportedException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "WebAuthnCredentialNotSupportedException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _WebAuthnCredentialNotSupportedException.prototype);
  }
};
var WebAuthnNotEnabledException = class _WebAuthnNotEnabledException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "WebAuthnNotEnabledException");
  }
  name = "WebAuthnNotEnabledException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "WebAuthnNotEnabledException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _WebAuthnNotEnabledException.prototype);
  }
};
var WebAuthnOriginNotAllowedException = class _WebAuthnOriginNotAllowedException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "WebAuthnOriginNotAllowedException");
  }
  name = "WebAuthnOriginNotAllowedException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "WebAuthnOriginNotAllowedException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _WebAuthnOriginNotAllowedException.prototype);
  }
};
var WebAuthnRelyingPartyMismatchException = class _WebAuthnRelyingPartyMismatchException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "WebAuthnRelyingPartyMismatchException");
  }
  name = "WebAuthnRelyingPartyMismatchException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "WebAuthnRelyingPartyMismatchException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _WebAuthnRelyingPartyMismatchException.prototype);
  }
};
var DeviceKeyExistsException = class _DeviceKeyExistsException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "DeviceKeyExistsException");
  }
  name = "DeviceKeyExistsException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "DeviceKeyExistsException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _DeviceKeyExistsException.prototype);
  }
};
var GroupExistsException = class _GroupExistsException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "GroupExistsException");
  }
  name = "GroupExistsException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "GroupExistsException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _GroupExistsException.prototype);
  }
};
var IdentityProviderTypeType = {
  Facebook: "Facebook",
  Google: "Google",
  LoginWithAmazon: "LoginWithAmazon",
  OIDC: "OIDC",
  SAML: "SAML",
  SignInWithApple: "SignInWithApple"
};
var DuplicateProviderException = class _DuplicateProviderException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "DuplicateProviderException");
  }
  name = "DuplicateProviderException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "DuplicateProviderException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _DuplicateProviderException.prototype);
  }
};
var ManagedLoginBrandingExistsException = class _ManagedLoginBrandingExistsException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "ManagedLoginBrandingExistsException");
  }
  name = "ManagedLoginBrandingExistsException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ManagedLoginBrandingExistsException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ManagedLoginBrandingExistsException.prototype);
  }
};
var UserImportJobStatusType = {
  Created: "Created",
  Expired: "Expired",
  Failed: "Failed",
  InProgress: "InProgress",
  Pending: "Pending",
  Stopped: "Stopped",
  Stopping: "Stopping",
  Succeeded: "Succeeded"
};
var DeletionProtectionType = {
  ACTIVE: "ACTIVE",
  INACTIVE: "INACTIVE"
};
var EmailSendingAccountType = {
  COGNITO_DEFAULT: "COGNITO_DEFAULT",
  DEVELOPER: "DEVELOPER"
};
var CustomEmailSenderLambdaVersionType = {
  V1_0: "V1_0"
};
var CustomSMSSenderLambdaVersionType = {
  V1_0: "V1_0"
};
var PreTokenGenerationLambdaVersionType = {
  V1_0: "V1_0",
  V2_0: "V2_0",
  V3_0: "V3_0"
};
var UserPoolMfaType = {
  OFF: "OFF",
  ON: "ON",
  OPTIONAL: "OPTIONAL"
};
var UsernameAttributeType = {
  EMAIL: "email",
  PHONE_NUMBER: "phone_number"
};
var UserPoolTierType = {
  ESSENTIALS: "ESSENTIALS",
  LITE: "LITE",
  PLUS: "PLUS"
};
var DefaultEmailOptionType = {
  CONFIRM_WITH_CODE: "CONFIRM_WITH_CODE",
  CONFIRM_WITH_LINK: "CONFIRM_WITH_LINK"
};
var StatusType = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var FeatureUnavailableInTierException = class _FeatureUnavailableInTierException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "FeatureUnavailableInTierException");
  }
  name = "FeatureUnavailableInTierException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "FeatureUnavailableInTierException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _FeatureUnavailableInTierException.prototype);
  }
};
var TierChangeNotAllowedException = class _TierChangeNotAllowedException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "TierChangeNotAllowedException");
  }
  name = "TierChangeNotAllowedException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "TierChangeNotAllowedException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _TierChangeNotAllowedException.prototype);
  }
};
var UserPoolTaggingException = class _UserPoolTaggingException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UserPoolTaggingException");
  }
  name = "UserPoolTaggingException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UserPoolTaggingException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UserPoolTaggingException.prototype);
  }
};
var OAuthFlowType = {
  client_credentials: "client_credentials",
  code: "code",
  implicit: "implicit"
};
var ExplicitAuthFlowsType = {
  ADMIN_NO_SRP_AUTH: "ADMIN_NO_SRP_AUTH",
  ALLOW_ADMIN_USER_PASSWORD_AUTH: "ALLOW_ADMIN_USER_PASSWORD_AUTH",
  ALLOW_CUSTOM_AUTH: "ALLOW_CUSTOM_AUTH",
  ALLOW_REFRESH_TOKEN_AUTH: "ALLOW_REFRESH_TOKEN_AUTH",
  ALLOW_USER_AUTH: "ALLOW_USER_AUTH",
  ALLOW_USER_PASSWORD_AUTH: "ALLOW_USER_PASSWORD_AUTH",
  ALLOW_USER_SRP_AUTH: "ALLOW_USER_SRP_AUTH",
  CUSTOM_AUTH_FLOW_ONLY: "CUSTOM_AUTH_FLOW_ONLY",
  USER_PASSWORD_AUTH: "USER_PASSWORD_AUTH"
};
var PreventUserExistenceErrorTypes = {
  ENABLED: "ENABLED",
  LEGACY: "LEGACY"
};
var FeatureType = {
  DISABLED: "DISABLED",
  ENABLED: "ENABLED"
};
var TimeUnitsType = {
  DAYS: "days",
  HOURS: "hours",
  MINUTES: "minutes",
  SECONDS: "seconds"
};
var InvalidOAuthFlowException = class _InvalidOAuthFlowException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "InvalidOAuthFlowException");
  }
  name = "InvalidOAuthFlowException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidOAuthFlowException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidOAuthFlowException.prototype);
  }
};
var ScopeDoesNotExistException = class _ScopeDoesNotExistException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "ScopeDoesNotExistException");
  }
  name = "ScopeDoesNotExistException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ScopeDoesNotExistException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ScopeDoesNotExistException.prototype);
  }
};
var UnsupportedIdentityProviderException = class _UnsupportedIdentityProviderException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UnsupportedIdentityProviderException");
  }
  name = "UnsupportedIdentityProviderException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UnsupportedIdentityProviderException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UnsupportedIdentityProviderException.prototype);
  }
};
var CompromisedCredentialsEventActionType = {
  BLOCK: "BLOCK",
  NO_ACTION: "NO_ACTION"
};
var EventFilterType = {
  PASSWORD_CHANGE: "PASSWORD_CHANGE",
  SIGN_IN: "SIGN_IN",
  SIGN_UP: "SIGN_UP"
};
var DomainStatusType = {
  ACTIVE: "ACTIVE",
  CREATING: "CREATING",
  DELETING: "DELETING",
  FAILED: "FAILED",
  UPDATING: "UPDATING"
};
var EventSourceName = {
  USER_AUTH_EVENTS: "userAuthEvents",
  USER_NOTIFICATION: "userNotification"
};
var LogLevel = {
  ERROR: "ERROR",
  INFO: "INFO"
};
var RefreshTokenReuseException = class _RefreshTokenReuseException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "RefreshTokenReuseException");
  }
  name = "RefreshTokenReuseException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "RefreshTokenReuseException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _RefreshTokenReuseException.prototype);
  }
};
var AdminAddUserToGroupRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminAddUserToGroupRequestFilterSensitiveLog");
var AdminConfirmSignUpRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminConfirmSignUpRequestFilterSensitiveLog");
var AttributeTypeFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Value && { Value: import_smithy_client.SENSITIVE_STRING }
}), "AttributeTypeFilterSensitiveLog");
var AdminCreateUserRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserAttributes && {
    UserAttributes: obj.UserAttributes.map((item) => AttributeTypeFilterSensitiveLog(item))
  },
  ...obj.ValidationData && {
    ValidationData: obj.ValidationData.map((item) => AttributeTypeFilterSensitiveLog(item))
  },
  ...obj.TemporaryPassword && { TemporaryPassword: import_smithy_client.SENSITIVE_STRING }
}), "AdminCreateUserRequestFilterSensitiveLog");
var UserTypeFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING },
  ...obj.Attributes && { Attributes: obj.Attributes.map((item) => AttributeTypeFilterSensitiveLog(item)) }
}), "UserTypeFilterSensitiveLog");
var AdminCreateUserResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.User && { User: UserTypeFilterSensitiveLog(obj.User) }
}), "AdminCreateUserResponseFilterSensitiveLog");
var AdminDeleteUserRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminDeleteUserRequestFilterSensitiveLog");
var AdminDeleteUserAttributesRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminDeleteUserAttributesRequestFilterSensitiveLog");
var AdminDisableUserRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminDisableUserRequestFilterSensitiveLog");
var AdminEnableUserRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminEnableUserRequestFilterSensitiveLog");
var AdminForgetDeviceRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminForgetDeviceRequestFilterSensitiveLog");
var AdminGetDeviceRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminGetDeviceRequestFilterSensitiveLog");
var DeviceTypeFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.DeviceAttributes && {
    DeviceAttributes: obj.DeviceAttributes.map((item) => AttributeTypeFilterSensitiveLog(item))
  }
}), "DeviceTypeFilterSensitiveLog");
var AdminGetDeviceResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Device && { Device: DeviceTypeFilterSensitiveLog(obj.Device) }
}), "AdminGetDeviceResponseFilterSensitiveLog");
var AdminGetUserRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminGetUserRequestFilterSensitiveLog");
var AdminGetUserResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserAttributes && {
    UserAttributes: obj.UserAttributes.map((item) => AttributeTypeFilterSensitiveLog(item))
  }
}), "AdminGetUserResponseFilterSensitiveLog");
var AdminInitiateAuthRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.AuthParameters && { AuthParameters: import_smithy_client.SENSITIVE_STRING },
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING }
}), "AdminInitiateAuthRequestFilterSensitiveLog");
var AuthenticationResultTypeFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING },
  ...obj.RefreshToken && { RefreshToken: import_smithy_client.SENSITIVE_STRING },
  ...obj.IdToken && { IdToken: import_smithy_client.SENSITIVE_STRING }
}), "AuthenticationResultTypeFilterSensitiveLog");
var AdminInitiateAuthResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING },
  ...obj.AuthenticationResult && {
    AuthenticationResult: AuthenticationResultTypeFilterSensitiveLog(obj.AuthenticationResult)
  }
}), "AdminInitiateAuthResponseFilterSensitiveLog");
var AdminListDevicesRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminListDevicesRequestFilterSensitiveLog");
var AdminListDevicesResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj
}), "AdminListDevicesResponseFilterSensitiveLog");
var AdminListGroupsForUserRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminListGroupsForUserRequestFilterSensitiveLog");
var AdminListUserAuthEventsRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminListUserAuthEventsRequestFilterSensitiveLog");
var AdminRemoveUserFromGroupRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminRemoveUserFromGroupRequestFilterSensitiveLog");
var AdminResetUserPasswordRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminResetUserPasswordRequestFilterSensitiveLog");
var AdminRespondToAuthChallengeRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.ChallengeResponses && { ChallengeResponses: import_smithy_client.SENSITIVE_STRING },
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING }
}), "AdminRespondToAuthChallengeRequestFilterSensitiveLog");
var AdminRespondToAuthChallengeResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING },
  ...obj.AuthenticationResult && {
    AuthenticationResult: AuthenticationResultTypeFilterSensitiveLog(obj.AuthenticationResult)
  }
}), "AdminRespondToAuthChallengeResponseFilterSensitiveLog");
var AdminSetUserMFAPreferenceRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminSetUserMFAPreferenceRequestFilterSensitiveLog");
var AdminSetUserPasswordRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING },
  ...obj.Password && { Password: import_smithy_client.SENSITIVE_STRING }
}), "AdminSetUserPasswordRequestFilterSensitiveLog");
var AdminSetUserSettingsRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminSetUserSettingsRequestFilterSensitiveLog");
var AdminUpdateAuthEventFeedbackRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminUpdateAuthEventFeedbackRequestFilterSensitiveLog");
var AdminUpdateDeviceStatusRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminUpdateDeviceStatusRequestFilterSensitiveLog");
var AdminUpdateUserAttributesRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserAttributes && {
    UserAttributes: obj.UserAttributes.map((item) => AttributeTypeFilterSensitiveLog(item))
  }
}), "AdminUpdateUserAttributesRequestFilterSensitiveLog");
var AdminUserGlobalSignOutRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "AdminUserGlobalSignOutRequestFilterSensitiveLog");
var AssociateSoftwareTokenRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING },
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING }
}), "AssociateSoftwareTokenRequestFilterSensitiveLog");
var AssociateSoftwareTokenResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SecretCode && { SecretCode: import_smithy_client.SENSITIVE_STRING },
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING }
}), "AssociateSoftwareTokenResponseFilterSensitiveLog");
var ChangePasswordRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.PreviousPassword && { PreviousPassword: import_smithy_client.SENSITIVE_STRING },
  ...obj.ProposedPassword && { ProposedPassword: import_smithy_client.SENSITIVE_STRING },
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "ChangePasswordRequestFilterSensitiveLog");
var CompleteWebAuthnRegistrationRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "CompleteWebAuthnRegistrationRequestFilterSensitiveLog");
var ConfirmDeviceRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "ConfirmDeviceRequestFilterSensitiveLog");
var UserContextDataTypeFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj
}), "UserContextDataTypeFilterSensitiveLog");
var ConfirmForgotPasswordRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SecretHash && { SecretHash: import_smithy_client.SENSITIVE_STRING },
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING },
  ...obj.Password && { Password: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserContextData && { UserContextData: import_smithy_client.SENSITIVE_STRING }
}), "ConfirmForgotPasswordRequestFilterSensitiveLog");
var ConfirmSignUpRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SecretHash && { SecretHash: import_smithy_client.SENSITIVE_STRING },
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserContextData && { UserContextData: import_smithy_client.SENSITIVE_STRING },
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING }
}), "ConfirmSignUpRequestFilterSensitiveLog");
var ConfirmSignUpResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING }
}), "ConfirmSignUpResponseFilterSensitiveLog");
var CreateManagedLoginBrandingRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "CreateManagedLoginBrandingRequestFilterSensitiveLog");
var UserPoolClientTypeFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.ClientSecret && { ClientSecret: import_smithy_client.SENSITIVE_STRING }
}), "UserPoolClientTypeFilterSensitiveLog");
var CreateUserPoolClientResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.UserPoolClient && { UserPoolClient: UserPoolClientTypeFilterSensitiveLog(obj.UserPoolClient) }
}), "CreateUserPoolClientResponseFilterSensitiveLog");
var DeleteUserRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "DeleteUserRequestFilterSensitiveLog");
var DeleteUserAttributesRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "DeleteUserAttributesRequestFilterSensitiveLog");
var DeleteUserPoolClientRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "DeleteUserPoolClientRequestFilterSensitiveLog");
var DeleteWebAuthnCredentialRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "DeleteWebAuthnCredentialRequestFilterSensitiveLog");
var DescribeManagedLoginBrandingByClientRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "DescribeManagedLoginBrandingByClientRequestFilterSensitiveLog");
var DescribeRiskConfigurationRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "DescribeRiskConfigurationRequestFilterSensitiveLog");
var RiskConfigurationTypeFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "RiskConfigurationTypeFilterSensitiveLog");
var DescribeRiskConfigurationResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.RiskConfiguration && { RiskConfiguration: RiskConfigurationTypeFilterSensitiveLog(obj.RiskConfiguration) }
}), "DescribeRiskConfigurationResponseFilterSensitiveLog");
var DescribeUserPoolClientRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "DescribeUserPoolClientRequestFilterSensitiveLog");
var DescribeUserPoolClientResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.UserPoolClient && { UserPoolClient: UserPoolClientTypeFilterSensitiveLog(obj.UserPoolClient) }
}), "DescribeUserPoolClientResponseFilterSensitiveLog");
var ForgetDeviceRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "ForgetDeviceRequestFilterSensitiveLog");
var ForgotPasswordRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SecretHash && { SecretHash: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserContextData && { UserContextData: import_smithy_client.SENSITIVE_STRING },
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "ForgotPasswordRequestFilterSensitiveLog");
var GetDeviceRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "GetDeviceRequestFilterSensitiveLog");
var GetDeviceResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Device && { Device: DeviceTypeFilterSensitiveLog(obj.Device) }
}), "GetDeviceResponseFilterSensitiveLog");
var GetTokensFromRefreshTokenRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.RefreshToken && { RefreshToken: import_smithy_client.SENSITIVE_STRING },
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.ClientSecret && { ClientSecret: import_smithy_client.SENSITIVE_STRING }
}), "GetTokensFromRefreshTokenRequestFilterSensitiveLog");
var GetTokensFromRefreshTokenResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AuthenticationResult && {
    AuthenticationResult: AuthenticationResultTypeFilterSensitiveLog(obj.AuthenticationResult)
  }
}), "GetTokensFromRefreshTokenResponseFilterSensitiveLog");
var GetUICustomizationRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "GetUICustomizationRequestFilterSensitiveLog");
var UICustomizationTypeFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "UICustomizationTypeFilterSensitiveLog");
var GetUICustomizationResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.UICustomization && { UICustomization: UICustomizationTypeFilterSensitiveLog(obj.UICustomization) }
}), "GetUICustomizationResponseFilterSensitiveLog");
var GetUserRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "GetUserRequestFilterSensitiveLog");
var GetUserResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserAttributes && {
    UserAttributes: obj.UserAttributes.map((item) => AttributeTypeFilterSensitiveLog(item))
  }
}), "GetUserResponseFilterSensitiveLog");
var GetUserAttributeVerificationCodeRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "GetUserAttributeVerificationCodeRequestFilterSensitiveLog");

// src/models/models_1.ts

var UserVerificationType = {
  PREFERRED: "preferred",
  REQUIRED: "required"
};
var UnauthorizedException = class _UnauthorizedException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UnauthorizedException");
  }
  name = "UnauthorizedException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UnauthorizedException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UnauthorizedException.prototype);
  }
};
var UnsupportedTokenTypeException = class _UnsupportedTokenTypeException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "UnsupportedTokenTypeException");
  }
  name = "UnsupportedTokenTypeException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UnsupportedTokenTypeException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UnsupportedTokenTypeException.prototype);
  }
};
var WebAuthnConfigurationMissingException = class _WebAuthnConfigurationMissingException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "WebAuthnConfigurationMissingException");
  }
  name = "WebAuthnConfigurationMissingException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "WebAuthnConfigurationMissingException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _WebAuthnConfigurationMissingException.prototype);
  }
};
var EnableSoftwareTokenMFAException = class _EnableSoftwareTokenMFAException extends CognitoIdentityProviderServiceException {
  static {
    __name(this, "EnableSoftwareTokenMFAException");
  }
  name = "EnableSoftwareTokenMFAException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "EnableSoftwareTokenMFAException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _EnableSoftwareTokenMFAException.prototype);
  }
};
var VerifySoftwareTokenResponseType = {
  ERROR: "ERROR",
  SUCCESS: "SUCCESS"
};
var GetUserAuthFactorsRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "GetUserAuthFactorsRequestFilterSensitiveLog");
var GetUserAuthFactorsResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "GetUserAuthFactorsResponseFilterSensitiveLog");
var GlobalSignOutRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "GlobalSignOutRequestFilterSensitiveLog");
var InitiateAuthRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AuthParameters && { AuthParameters: import_smithy_client.SENSITIVE_STRING },
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserContextData && { UserContextData: import_smithy_client.SENSITIVE_STRING },
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING }
}), "InitiateAuthRequestFilterSensitiveLog");
var InitiateAuthResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING },
  ...obj.AuthenticationResult && {
    AuthenticationResult: AuthenticationResultTypeFilterSensitiveLog(obj.AuthenticationResult)
  }
}), "InitiateAuthResponseFilterSensitiveLog");
var ListDevicesRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "ListDevicesRequestFilterSensitiveLog");
var ListDevicesResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj
}), "ListDevicesResponseFilterSensitiveLog");
var UserPoolClientDescriptionFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "UserPoolClientDescriptionFilterSensitiveLog");
var ListUserPoolClientsResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.UserPoolClients && {
    UserPoolClients: obj.UserPoolClients.map((item) => UserPoolClientDescriptionFilterSensitiveLog(item))
  }
}), "ListUserPoolClientsResponseFilterSensitiveLog");
var ListUsersResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Users && { Users: obj.Users.map((item) => UserTypeFilterSensitiveLog(item)) }
}), "ListUsersResponseFilterSensitiveLog");
var ListUsersInGroupResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Users && { Users: obj.Users.map((item) => UserTypeFilterSensitiveLog(item)) }
}), "ListUsersInGroupResponseFilterSensitiveLog");
var ListWebAuthnCredentialsRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "ListWebAuthnCredentialsRequestFilterSensitiveLog");
var ResendConfirmationCodeRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SecretHash && { SecretHash: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserContextData && { UserContextData: import_smithy_client.SENSITIVE_STRING },
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING }
}), "ResendConfirmationCodeRequestFilterSensitiveLog");
var RespondToAuthChallengeRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING },
  ...obj.ChallengeResponses && { ChallengeResponses: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserContextData && { UserContextData: import_smithy_client.SENSITIVE_STRING }
}), "RespondToAuthChallengeRequestFilterSensitiveLog");
var RespondToAuthChallengeResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING },
  ...obj.AuthenticationResult && {
    AuthenticationResult: AuthenticationResultTypeFilterSensitiveLog(obj.AuthenticationResult)
  }
}), "RespondToAuthChallengeResponseFilterSensitiveLog");
var RevokeTokenRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Token && { Token: import_smithy_client.SENSITIVE_STRING },
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.ClientSecret && { ClientSecret: import_smithy_client.SENSITIVE_STRING }
}), "RevokeTokenRequestFilterSensitiveLog");
var SetRiskConfigurationRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "SetRiskConfigurationRequestFilterSensitiveLog");
var SetRiskConfigurationResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.RiskConfiguration && { RiskConfiguration: RiskConfigurationTypeFilterSensitiveLog(obj.RiskConfiguration) }
}), "SetRiskConfigurationResponseFilterSensitiveLog");
var SetUICustomizationRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "SetUICustomizationRequestFilterSensitiveLog");
var SetUICustomizationResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.UICustomization && { UICustomization: UICustomizationTypeFilterSensitiveLog(obj.UICustomization) }
}), "SetUICustomizationResponseFilterSensitiveLog");
var SetUserMFAPreferenceRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "SetUserMFAPreferenceRequestFilterSensitiveLog");
var SetUserSettingsRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "SetUserSettingsRequestFilterSensitiveLog");
var SignUpRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SecretHash && { SecretHash: import_smithy_client.SENSITIVE_STRING },
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING },
  ...obj.Password && { Password: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserAttributes && {
    UserAttributes: obj.UserAttributes.map((item) => AttributeTypeFilterSensitiveLog(item))
  },
  ...obj.ValidationData && {
    ValidationData: obj.ValidationData.map((item) => AttributeTypeFilterSensitiveLog(item))
  },
  ...obj.UserContextData && { UserContextData: import_smithy_client.SENSITIVE_STRING }
}), "SignUpRequestFilterSensitiveLog");
var SignUpResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING }
}), "SignUpResponseFilterSensitiveLog");
var StartWebAuthnRegistrationRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "StartWebAuthnRegistrationRequestFilterSensitiveLog");
var UpdateAuthEventFeedbackRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Username && { Username: import_smithy_client.SENSITIVE_STRING },
  ...obj.FeedbackToken && { FeedbackToken: import_smithy_client.SENSITIVE_STRING }
}), "UpdateAuthEventFeedbackRequestFilterSensitiveLog");
var UpdateDeviceStatusRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "UpdateDeviceStatusRequestFilterSensitiveLog");
var UpdateUserAttributesRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.UserAttributes && {
    UserAttributes: obj.UserAttributes.map((item) => AttributeTypeFilterSensitiveLog(item))
  },
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "UpdateUserAttributesRequestFilterSensitiveLog");
var UpdateUserPoolClientRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ClientId && { ClientId: import_smithy_client.SENSITIVE_STRING }
}), "UpdateUserPoolClientRequestFilterSensitiveLog");
var UpdateUserPoolClientResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.UserPoolClient && { UserPoolClient: UserPoolClientTypeFilterSensitiveLog(obj.UserPoolClient) }
}), "UpdateUserPoolClientResponseFilterSensitiveLog");
var VerifySoftwareTokenRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING },
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING },
  ...obj.UserCode && { UserCode: import_smithy_client.SENSITIVE_STRING }
}), "VerifySoftwareTokenRequestFilterSensitiveLog");
var VerifySoftwareTokenResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Session && { Session: import_smithy_client.SENSITIVE_STRING }
}), "VerifySoftwareTokenResponseFilterSensitiveLog");
var VerifyUserAttributeRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AccessToken && { AccessToken: import_smithy_client.SENSITIVE_STRING }
}), "VerifyUserAttributeRequestFilterSensitiveLog");

// src/protocols/Aws_json1_1.ts
var se_AddCustomAttributesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AddCustomAttributes");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AddCustomAttributesCommand");
var se_AdminAddUserToGroupCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminAddUserToGroup");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminAddUserToGroupCommand");
var se_AdminConfirmSignUpCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminConfirmSignUp");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminConfirmSignUpCommand");
var se_AdminCreateUserCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminCreateUser");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminCreateUserCommand");
var se_AdminDeleteUserCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminDeleteUser");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminDeleteUserCommand");
var se_AdminDeleteUserAttributesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminDeleteUserAttributes");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminDeleteUserAttributesCommand");
var se_AdminDisableProviderForUserCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminDisableProviderForUser");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminDisableProviderForUserCommand");
var se_AdminDisableUserCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminDisableUser");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminDisableUserCommand");
var se_AdminEnableUserCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminEnableUser");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminEnableUserCommand");
var se_AdminForgetDeviceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminForgetDevice");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminForgetDeviceCommand");
var se_AdminGetDeviceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminGetDevice");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminGetDeviceCommand");
var se_AdminGetUserCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminGetUser");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminGetUserCommand");
var se_AdminInitiateAuthCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminInitiateAuth");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminInitiateAuthCommand");
var se_AdminLinkProviderForUserCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminLinkProviderForUser");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminLinkProviderForUserCommand");
var se_AdminListDevicesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminListDevices");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminListDevicesCommand");
var se_AdminListGroupsForUserCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminListGroupsForUser");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminListGroupsForUserCommand");
var se_AdminListUserAuthEventsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminListUserAuthEvents");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminListUserAuthEventsCommand");
var se_AdminRemoveUserFromGroupCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminRemoveUserFromGroup");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminRemoveUserFromGroupCommand");
var se_AdminResetUserPasswordCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminResetUserPassword");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminResetUserPasswordCommand");
var se_AdminRespondToAuthChallengeCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminRespondToAuthChallenge");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminRespondToAuthChallengeCommand");
var se_AdminSetUserMFAPreferenceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminSetUserMFAPreference");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminSetUserMFAPreferenceCommand");
var se_AdminSetUserPasswordCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminSetUserPassword");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminSetUserPasswordCommand");
var se_AdminSetUserSettingsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminSetUserSettings");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminSetUserSettingsCommand");
var se_AdminUpdateAuthEventFeedbackCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminUpdateAuthEventFeedback");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminUpdateAuthEventFeedbackCommand");
var se_AdminUpdateDeviceStatusCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminUpdateDeviceStatus");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminUpdateDeviceStatusCommand");
var se_AdminUpdateUserAttributesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminUpdateUserAttributes");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminUpdateUserAttributesCommand");
var se_AdminUserGlobalSignOutCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AdminUserGlobalSignOut");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AdminUserGlobalSignOutCommand");
var se_AssociateSoftwareTokenCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AssociateSoftwareToken");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AssociateSoftwareTokenCommand");
var se_ChangePasswordCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ChangePassword");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ChangePasswordCommand");
var se_CompleteWebAuthnRegistrationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CompleteWebAuthnRegistration");
  let body;
  body = JSON.stringify(se_CompleteWebAuthnRegistrationRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CompleteWebAuthnRegistrationCommand");
var se_ConfirmDeviceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ConfirmDevice");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ConfirmDeviceCommand");
var se_ConfirmForgotPasswordCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ConfirmForgotPassword");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ConfirmForgotPasswordCommand");
var se_ConfirmSignUpCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ConfirmSignUp");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ConfirmSignUpCommand");
var se_CreateGroupCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateGroup");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateGroupCommand");
var se_CreateIdentityProviderCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateIdentityProvider");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateIdentityProviderCommand");
var se_CreateManagedLoginBrandingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateManagedLoginBranding");
  let body;
  body = JSON.stringify(se_CreateManagedLoginBrandingRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateManagedLoginBrandingCommand");
var se_CreateResourceServerCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateResourceServer");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateResourceServerCommand");
var se_CreateUserImportJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateUserImportJob");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateUserImportJobCommand");
var se_CreateUserPoolCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateUserPool");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateUserPoolCommand");
var se_CreateUserPoolClientCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateUserPoolClient");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateUserPoolClientCommand");
var se_CreateUserPoolDomainCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateUserPoolDomain");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateUserPoolDomainCommand");
var se_DeleteGroupCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteGroup");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteGroupCommand");
var se_DeleteIdentityProviderCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteIdentityProvider");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteIdentityProviderCommand");
var se_DeleteManagedLoginBrandingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteManagedLoginBranding");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteManagedLoginBrandingCommand");
var se_DeleteResourceServerCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteResourceServer");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteResourceServerCommand");
var se_DeleteUserCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteUser");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteUserCommand");
var se_DeleteUserAttributesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteUserAttributes");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteUserAttributesCommand");
var se_DeleteUserPoolCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteUserPool");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteUserPoolCommand");
var se_DeleteUserPoolClientCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteUserPoolClient");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteUserPoolClientCommand");
var se_DeleteUserPoolDomainCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteUserPoolDomain");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteUserPoolDomainCommand");
var se_DeleteWebAuthnCredentialCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteWebAuthnCredential");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteWebAuthnCredentialCommand");
var se_DescribeIdentityProviderCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DescribeIdentityProvider");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DescribeIdentityProviderCommand");
var se_DescribeManagedLoginBrandingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DescribeManagedLoginBranding");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DescribeManagedLoginBrandingCommand");
var se_DescribeManagedLoginBrandingByClientCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DescribeManagedLoginBrandingByClient");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DescribeManagedLoginBrandingByClientCommand");
var se_DescribeResourceServerCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DescribeResourceServer");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DescribeResourceServerCommand");
var se_DescribeRiskConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DescribeRiskConfiguration");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DescribeRiskConfigurationCommand");
var se_DescribeUserImportJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DescribeUserImportJob");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DescribeUserImportJobCommand");
var se_DescribeUserPoolCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DescribeUserPool");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DescribeUserPoolCommand");
var se_DescribeUserPoolClientCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DescribeUserPoolClient");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DescribeUserPoolClientCommand");
var se_DescribeUserPoolDomainCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DescribeUserPoolDomain");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DescribeUserPoolDomainCommand");
var se_ForgetDeviceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ForgetDevice");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ForgetDeviceCommand");
var se_ForgotPasswordCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ForgotPassword");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ForgotPasswordCommand");
var se_GetCSVHeaderCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetCSVHeader");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetCSVHeaderCommand");
var se_GetDeviceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetDevice");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetDeviceCommand");
var se_GetGroupCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetGroup");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetGroupCommand");
var se_GetIdentityProviderByIdentifierCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetIdentityProviderByIdentifier");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetIdentityProviderByIdentifierCommand");
var se_GetLogDeliveryConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetLogDeliveryConfiguration");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetLogDeliveryConfigurationCommand");
var se_GetSigningCertificateCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetSigningCertificate");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetSigningCertificateCommand");
var se_GetTokensFromRefreshTokenCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetTokensFromRefreshToken");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetTokensFromRefreshTokenCommand");
var se_GetUICustomizationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetUICustomization");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetUICustomizationCommand");
var se_GetUserCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetUser");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetUserCommand");
var se_GetUserAttributeVerificationCodeCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetUserAttributeVerificationCode");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetUserAttributeVerificationCodeCommand");
var se_GetUserAuthFactorsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetUserAuthFactors");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetUserAuthFactorsCommand");
var se_GetUserPoolMfaConfigCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetUserPoolMfaConfig");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetUserPoolMfaConfigCommand");
var se_GlobalSignOutCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GlobalSignOut");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GlobalSignOutCommand");
var se_InitiateAuthCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("InitiateAuth");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_InitiateAuthCommand");
var se_ListDevicesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListDevices");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListDevicesCommand");
var se_ListGroupsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListGroups");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListGroupsCommand");
var se_ListIdentityProvidersCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListIdentityProviders");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListIdentityProvidersCommand");
var se_ListResourceServersCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListResourceServers");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListResourceServersCommand");
var se_ListTagsForResourceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListTagsForResource");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListTagsForResourceCommand");
var se_ListUserImportJobsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListUserImportJobs");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListUserImportJobsCommand");
var se_ListUserPoolClientsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListUserPoolClients");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListUserPoolClientsCommand");
var se_ListUserPoolsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListUserPools");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListUserPoolsCommand");
var se_ListUsersCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListUsers");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListUsersCommand");
var se_ListUsersInGroupCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListUsersInGroup");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListUsersInGroupCommand");
var se_ListWebAuthnCredentialsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListWebAuthnCredentials");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListWebAuthnCredentialsCommand");
var se_ResendConfirmationCodeCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ResendConfirmationCode");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ResendConfirmationCodeCommand");
var se_RespondToAuthChallengeCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("RespondToAuthChallenge");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_RespondToAuthChallengeCommand");
var se_RevokeTokenCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("RevokeToken");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_RevokeTokenCommand");
var se_SetLogDeliveryConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("SetLogDeliveryConfiguration");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_SetLogDeliveryConfigurationCommand");
var se_SetRiskConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("SetRiskConfiguration");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_SetRiskConfigurationCommand");
var se_SetUICustomizationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("SetUICustomization");
  let body;
  body = JSON.stringify(se_SetUICustomizationRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_SetUICustomizationCommand");
var se_SetUserMFAPreferenceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("SetUserMFAPreference");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_SetUserMFAPreferenceCommand");
var se_SetUserPoolMfaConfigCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("SetUserPoolMfaConfig");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_SetUserPoolMfaConfigCommand");
var se_SetUserSettingsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("SetUserSettings");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_SetUserSettingsCommand");
var se_SignUpCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("SignUp");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_SignUpCommand");
var se_StartUserImportJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("StartUserImportJob");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_StartUserImportJobCommand");
var se_StartWebAuthnRegistrationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("StartWebAuthnRegistration");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_StartWebAuthnRegistrationCommand");
var se_StopUserImportJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("StopUserImportJob");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_StopUserImportJobCommand");
var se_TagResourceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("TagResource");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_TagResourceCommand");
var se_UntagResourceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UntagResource");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UntagResourceCommand");
var se_UpdateAuthEventFeedbackCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateAuthEventFeedback");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateAuthEventFeedbackCommand");
var se_UpdateDeviceStatusCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateDeviceStatus");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateDeviceStatusCommand");
var se_UpdateGroupCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateGroup");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateGroupCommand");
var se_UpdateIdentityProviderCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateIdentityProvider");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateIdentityProviderCommand");
var se_UpdateManagedLoginBrandingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateManagedLoginBranding");
  let body;
  body = JSON.stringify(se_UpdateManagedLoginBrandingRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateManagedLoginBrandingCommand");
var se_UpdateResourceServerCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateResourceServer");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateResourceServerCommand");
var se_UpdateUserAttributesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateUserAttributes");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateUserAttributesCommand");
var se_UpdateUserPoolCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateUserPool");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateUserPoolCommand");
var se_UpdateUserPoolClientCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateUserPoolClient");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateUserPoolClientCommand");
var se_UpdateUserPoolDomainCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateUserPoolDomain");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateUserPoolDomainCommand");
var se_VerifySoftwareTokenCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("VerifySoftwareToken");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_VerifySoftwareTokenCommand");
var se_VerifyUserAttributeCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("VerifyUserAttribute");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_VerifyUserAttributeCommand");
var de_AddCustomAttributesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AddCustomAttributesCommand");
var de_AdminAddUserToGroupCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_AdminAddUserToGroupCommand");
var de_AdminConfirmSignUpCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminConfirmSignUpCommand");
var de_AdminCreateUserCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_AdminCreateUserResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminCreateUserCommand");
var de_AdminDeleteUserCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_AdminDeleteUserCommand");
var de_AdminDeleteUserAttributesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminDeleteUserAttributesCommand");
var de_AdminDisableProviderForUserCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminDisableProviderForUserCommand");
var de_AdminDisableUserCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminDisableUserCommand");
var de_AdminEnableUserCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminEnableUserCommand");
var de_AdminForgetDeviceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_AdminForgetDeviceCommand");
var de_AdminGetDeviceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_AdminGetDeviceResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminGetDeviceCommand");
var de_AdminGetUserCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_AdminGetUserResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminGetUserCommand");
var de_AdminInitiateAuthCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminInitiateAuthCommand");
var de_AdminLinkProviderForUserCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminLinkProviderForUserCommand");
var de_AdminListDevicesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_AdminListDevicesResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminListDevicesCommand");
var de_AdminListGroupsForUserCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_AdminListGroupsForUserResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminListGroupsForUserCommand");
var de_AdminListUserAuthEventsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_AdminListUserAuthEventsResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminListUserAuthEventsCommand");
var de_AdminRemoveUserFromGroupCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_AdminRemoveUserFromGroupCommand");
var de_AdminResetUserPasswordCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminResetUserPasswordCommand");
var de_AdminRespondToAuthChallengeCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminRespondToAuthChallengeCommand");
var de_AdminSetUserMFAPreferenceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminSetUserMFAPreferenceCommand");
var de_AdminSetUserPasswordCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminSetUserPasswordCommand");
var de_AdminSetUserSettingsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminSetUserSettingsCommand");
var de_AdminUpdateAuthEventFeedbackCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminUpdateAuthEventFeedbackCommand");
var de_AdminUpdateDeviceStatusCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminUpdateDeviceStatusCommand");
var de_AdminUpdateUserAttributesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminUpdateUserAttributesCommand");
var de_AdminUserGlobalSignOutCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AdminUserGlobalSignOutCommand");
var de_AssociateSoftwareTokenCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AssociateSoftwareTokenCommand");
var de_ChangePasswordCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ChangePasswordCommand");
var de_CompleteWebAuthnRegistrationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CompleteWebAuthnRegistrationCommand");
var de_ConfirmDeviceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ConfirmDeviceCommand");
var de_ConfirmForgotPasswordCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ConfirmForgotPasswordCommand");
var de_ConfirmSignUpCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ConfirmSignUpCommand");
var de_CreateGroupCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_CreateGroupResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateGroupCommand");
var de_CreateIdentityProviderCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_CreateIdentityProviderResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateIdentityProviderCommand");
var de_CreateManagedLoginBrandingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_CreateManagedLoginBrandingResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateManagedLoginBrandingCommand");
var de_CreateResourceServerCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateResourceServerCommand");
var de_CreateUserImportJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_CreateUserImportJobResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateUserImportJobCommand");
var de_CreateUserPoolCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_CreateUserPoolResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateUserPoolCommand");
var de_CreateUserPoolClientCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_CreateUserPoolClientResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateUserPoolClientCommand");
var de_CreateUserPoolDomainCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateUserPoolDomainCommand");
var de_DeleteGroupCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_DeleteGroupCommand");
var de_DeleteIdentityProviderCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_DeleteIdentityProviderCommand");
var de_DeleteManagedLoginBrandingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_DeleteManagedLoginBrandingCommand");
var de_DeleteResourceServerCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_DeleteResourceServerCommand");
var de_DeleteUserCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_DeleteUserCommand");
var de_DeleteUserAttributesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DeleteUserAttributesCommand");
var de_DeleteUserPoolCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_DeleteUserPoolCommand");
var de_DeleteUserPoolClientCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_DeleteUserPoolClientCommand");
var de_DeleteUserPoolDomainCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DeleteUserPoolDomainCommand");
var de_DeleteWebAuthnCredentialCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DeleteWebAuthnCredentialCommand");
var de_DescribeIdentityProviderCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_DescribeIdentityProviderResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DescribeIdentityProviderCommand");
var de_DescribeManagedLoginBrandingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_DescribeManagedLoginBrandingResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DescribeManagedLoginBrandingCommand");
var de_DescribeManagedLoginBrandingByClientCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_DescribeManagedLoginBrandingByClientResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DescribeManagedLoginBrandingByClientCommand");
var de_DescribeResourceServerCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DescribeResourceServerCommand");
var de_DescribeRiskConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_DescribeRiskConfigurationResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DescribeRiskConfigurationCommand");
var de_DescribeUserImportJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_DescribeUserImportJobResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DescribeUserImportJobCommand");
var de_DescribeUserPoolCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_DescribeUserPoolResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DescribeUserPoolCommand");
var de_DescribeUserPoolClientCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_DescribeUserPoolClientResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DescribeUserPoolClientCommand");
var de_DescribeUserPoolDomainCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DescribeUserPoolDomainCommand");
var de_ForgetDeviceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_ForgetDeviceCommand");
var de_ForgotPasswordCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ForgotPasswordCommand");
var de_GetCSVHeaderCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetCSVHeaderCommand");
var de_GetDeviceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_GetDeviceResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetDeviceCommand");
var de_GetGroupCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_GetGroupResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetGroupCommand");
var de_GetIdentityProviderByIdentifierCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_GetIdentityProviderByIdentifierResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetIdentityProviderByIdentifierCommand");
var de_GetLogDeliveryConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetLogDeliveryConfigurationCommand");
var de_GetSigningCertificateCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetSigningCertificateCommand");
var de_GetTokensFromRefreshTokenCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetTokensFromRefreshTokenCommand");
var de_GetUICustomizationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_GetUICustomizationResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetUICustomizationCommand");
var de_GetUserCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetUserCommand");
var de_GetUserAttributeVerificationCodeCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetUserAttributeVerificationCodeCommand");
var de_GetUserAuthFactorsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetUserAuthFactorsCommand");
var de_GetUserPoolMfaConfigCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetUserPoolMfaConfigCommand");
var de_GlobalSignOutCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GlobalSignOutCommand");
var de_InitiateAuthCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_InitiateAuthCommand");
var de_ListDevicesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ListDevicesResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListDevicesCommand");
var de_ListGroupsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ListGroupsResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListGroupsCommand");
var de_ListIdentityProvidersCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ListIdentityProvidersResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListIdentityProvidersCommand");
var de_ListResourceServersCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListResourceServersCommand");
var de_ListTagsForResourceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListTagsForResourceCommand");
var de_ListUserImportJobsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ListUserImportJobsResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListUserImportJobsCommand");
var de_ListUserPoolClientsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListUserPoolClientsCommand");
var de_ListUserPoolsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ListUserPoolsResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListUserPoolsCommand");
var de_ListUsersCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ListUsersResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListUsersCommand");
var de_ListUsersInGroupCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ListUsersInGroupResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListUsersInGroupCommand");
var de_ListWebAuthnCredentialsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ListWebAuthnCredentialsResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListWebAuthnCredentialsCommand");
var de_ResendConfirmationCodeCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ResendConfirmationCodeCommand");
var de_RespondToAuthChallengeCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_RespondToAuthChallengeCommand");
var de_RevokeTokenCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_RevokeTokenCommand");
var de_SetLogDeliveryConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_SetLogDeliveryConfigurationCommand");
var de_SetRiskConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_SetRiskConfigurationResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_SetRiskConfigurationCommand");
var de_SetUICustomizationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_SetUICustomizationResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_SetUICustomizationCommand");
var de_SetUserMFAPreferenceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_SetUserMFAPreferenceCommand");
var de_SetUserPoolMfaConfigCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_SetUserPoolMfaConfigCommand");
var de_SetUserSettingsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_SetUserSettingsCommand");
var de_SignUpCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_SignUpCommand");
var de_StartUserImportJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_StartUserImportJobResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_StartUserImportJobCommand");
var de_StartWebAuthnRegistrationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_StartWebAuthnRegistrationResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_StartWebAuthnRegistrationCommand");
var de_StopUserImportJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_StopUserImportJobResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_StopUserImportJobCommand");
var de_TagResourceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_TagResourceCommand");
var de_UntagResourceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UntagResourceCommand");
var de_UpdateAuthEventFeedbackCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateAuthEventFeedbackCommand");
var de_UpdateDeviceStatusCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateDeviceStatusCommand");
var de_UpdateGroupCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_UpdateGroupResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateGroupCommand");
var de_UpdateIdentityProviderCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_UpdateIdentityProviderResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateIdentityProviderCommand");
var de_UpdateManagedLoginBrandingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_UpdateManagedLoginBrandingResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateManagedLoginBrandingCommand");
var de_UpdateResourceServerCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateResourceServerCommand");
var de_UpdateUserAttributesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateUserAttributesCommand");
var de_UpdateUserPoolCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateUserPoolCommand");
var de_UpdateUserPoolClientCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_UpdateUserPoolClientResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateUserPoolClientCommand");
var de_UpdateUserPoolDomainCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateUserPoolDomainCommand");
var de_VerifySoftwareTokenCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_VerifySoftwareTokenCommand");
var de_VerifyUserAttributeCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_VerifyUserAttributeCommand");
var de_CommandError = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonErrorBody)(output.body, context)
  };
  const errorCode = (0, import_core2.loadRestJsonErrorCode)(output, parsedOutput.body);
  switch (errorCode) {
    case "InternalErrorException":
    case "com.amazonaws.cognitoidentityprovider#InternalErrorException":
      throw await de_InternalErrorExceptionRes(parsedOutput, context);
    case "InvalidParameterException":
    case "com.amazonaws.cognitoidentityprovider#InvalidParameterException":
      throw await de_InvalidParameterExceptionRes(parsedOutput, context);
    case "NotAuthorizedException":
    case "com.amazonaws.cognitoidentityprovider#NotAuthorizedException":
      throw await de_NotAuthorizedExceptionRes(parsedOutput, context);
    case "ResourceNotFoundException":
    case "com.amazonaws.cognitoidentityprovider#ResourceNotFoundException":
      throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);
    case "TooManyRequestsException":
    case "com.amazonaws.cognitoidentityprovider#TooManyRequestsException":
      throw await de_TooManyRequestsExceptionRes(parsedOutput, context);
    case "UserImportInProgressException":
    case "com.amazonaws.cognitoidentityprovider#UserImportInProgressException":
      throw await de_UserImportInProgressExceptionRes(parsedOutput, context);
    case "UserNotFoundException":
    case "com.amazonaws.cognitoidentityprovider#UserNotFoundException":
      throw await de_UserNotFoundExceptionRes(parsedOutput, context);
    case "InvalidLambdaResponseException":
    case "com.amazonaws.cognitoidentityprovider#InvalidLambdaResponseException":
      throw await de_InvalidLambdaResponseExceptionRes(parsedOutput, context);
    case "LimitExceededException":
    case "com.amazonaws.cognitoidentityprovider#LimitExceededException":
      throw await de_LimitExceededExceptionRes(parsedOutput, context);
    case "TooManyFailedAttemptsException":
    case "com.amazonaws.cognitoidentityprovider#TooManyFailedAttemptsException":
      throw await de_TooManyFailedAttemptsExceptionRes(parsedOutput, context);
    case "UnexpectedLambdaException":
    case "com.amazonaws.cognitoidentityprovider#UnexpectedLambdaException":
      throw await de_UnexpectedLambdaExceptionRes(parsedOutput, context);
    case "UserLambdaValidationException":
    case "com.amazonaws.cognitoidentityprovider#UserLambdaValidationException":
      throw await de_UserLambdaValidationExceptionRes(parsedOutput, context);
    case "CodeDeliveryFailureException":
    case "com.amazonaws.cognitoidentityprovider#CodeDeliveryFailureException":
      throw await de_CodeDeliveryFailureExceptionRes(parsedOutput, context);
    case "InvalidPasswordException":
    case "com.amazonaws.cognitoidentityprovider#InvalidPasswordException":
      throw await de_InvalidPasswordExceptionRes(parsedOutput, context);
    case "InvalidSmsRoleAccessPolicyException":
    case "com.amazonaws.cognitoidentityprovider#InvalidSmsRoleAccessPolicyException":
      throw await de_InvalidSmsRoleAccessPolicyExceptionRes(parsedOutput, context);
    case "InvalidSmsRoleTrustRelationshipException":
    case "com.amazonaws.cognitoidentityprovider#InvalidSmsRoleTrustRelationshipException":
      throw await de_InvalidSmsRoleTrustRelationshipExceptionRes(parsedOutput, context);
    case "PreconditionNotMetException":
    case "com.amazonaws.cognitoidentityprovider#PreconditionNotMetException":
      throw await de_PreconditionNotMetExceptionRes(parsedOutput, context);
    case "UnsupportedUserStateException":
    case "com.amazonaws.cognitoidentityprovider#UnsupportedUserStateException":
      throw await de_UnsupportedUserStateExceptionRes(parsedOutput, context);
    case "UsernameExistsException":
    case "com.amazonaws.cognitoidentityprovider#UsernameExistsException":
      throw await de_UsernameExistsExceptionRes(parsedOutput, context);
    case "AliasExistsException":
    case "com.amazonaws.cognitoidentityprovider#AliasExistsException":
      throw await de_AliasExistsExceptionRes(parsedOutput, context);
    case "InvalidUserPoolConfigurationException":
    case "com.amazonaws.cognitoidentityprovider#InvalidUserPoolConfigurationException":
      throw await de_InvalidUserPoolConfigurationExceptionRes(parsedOutput, context);
    case "InvalidEmailRoleAccessPolicyException":
    case "com.amazonaws.cognitoidentityprovider#InvalidEmailRoleAccessPolicyException":
      throw await de_InvalidEmailRoleAccessPolicyExceptionRes(parsedOutput, context);
    case "MFAMethodNotFoundException":
    case "com.amazonaws.cognitoidentityprovider#MFAMethodNotFoundException":
      throw await de_MFAMethodNotFoundExceptionRes(parsedOutput, context);
    case "PasswordResetRequiredException":
    case "com.amazonaws.cognitoidentityprovider#PasswordResetRequiredException":
      throw await de_PasswordResetRequiredExceptionRes(parsedOutput, context);
    case "UnsupportedOperationException":
    case "com.amazonaws.cognitoidentityprovider#UnsupportedOperationException":
      throw await de_UnsupportedOperationExceptionRes(parsedOutput, context);
    case "UserNotConfirmedException":
    case "com.amazonaws.cognitoidentityprovider#UserNotConfirmedException":
      throw await de_UserNotConfirmedExceptionRes(parsedOutput, context);
    case "UserPoolAddOnNotEnabledException":
    case "com.amazonaws.cognitoidentityprovider#UserPoolAddOnNotEnabledException":
      throw await de_UserPoolAddOnNotEnabledExceptionRes(parsedOutput, context);
    case "CodeMismatchException":
    case "com.amazonaws.cognitoidentityprovider#CodeMismatchException":
      throw await de_CodeMismatchExceptionRes(parsedOutput, context);
    case "ExpiredCodeException":
    case "com.amazonaws.cognitoidentityprovider#ExpiredCodeException":
      throw await de_ExpiredCodeExceptionRes(parsedOutput, context);
    case "PasswordHistoryPolicyViolationException":
    case "com.amazonaws.cognitoidentityprovider#PasswordHistoryPolicyViolationException":
      throw await de_PasswordHistoryPolicyViolationExceptionRes(parsedOutput, context);
    case "SoftwareTokenMFANotFoundException":
    case "com.amazonaws.cognitoidentityprovider#SoftwareTokenMFANotFoundException":
      throw await de_SoftwareTokenMFANotFoundExceptionRes(parsedOutput, context);
    case "ConcurrentModificationException":
    case "com.amazonaws.cognitoidentityprovider#ConcurrentModificationException":
      throw await de_ConcurrentModificationExceptionRes(parsedOutput, context);
    case "ForbiddenException":
    case "com.amazonaws.cognitoidentityprovider#ForbiddenException":
      throw await de_ForbiddenExceptionRes(parsedOutput, context);
    case "WebAuthnChallengeNotFoundException":
    case "com.amazonaws.cognitoidentityprovider#WebAuthnChallengeNotFoundException":
      throw await de_WebAuthnChallengeNotFoundExceptionRes(parsedOutput, context);
    case "WebAuthnClientMismatchException":
    case "com.amazonaws.cognitoidentityprovider#WebAuthnClientMismatchException":
      throw await de_WebAuthnClientMismatchExceptionRes(parsedOutput, context);
    case "WebAuthnCredentialNotSupportedException":
    case "com.amazonaws.cognitoidentityprovider#WebAuthnCredentialNotSupportedException":
      throw await de_WebAuthnCredentialNotSupportedExceptionRes(parsedOutput, context);
    case "WebAuthnNotEnabledException":
    case "com.amazonaws.cognitoidentityprovider#WebAuthnNotEnabledException":
      throw await de_WebAuthnNotEnabledExceptionRes(parsedOutput, context);
    case "WebAuthnOriginNotAllowedException":
    case "com.amazonaws.cognitoidentityprovider#WebAuthnOriginNotAllowedException":
      throw await de_WebAuthnOriginNotAllowedExceptionRes(parsedOutput, context);
    case "WebAuthnRelyingPartyMismatchException":
    case "com.amazonaws.cognitoidentityprovider#WebAuthnRelyingPartyMismatchException":
      throw await de_WebAuthnRelyingPartyMismatchExceptionRes(parsedOutput, context);
    case "DeviceKeyExistsException":
    case "com.amazonaws.cognitoidentityprovider#DeviceKeyExistsException":
      throw await de_DeviceKeyExistsExceptionRes(parsedOutput, context);
    case "GroupExistsException":
    case "com.amazonaws.cognitoidentityprovider#GroupExistsException":
      throw await de_GroupExistsExceptionRes(parsedOutput, context);
    case "DuplicateProviderException":
    case "com.amazonaws.cognitoidentityprovider#DuplicateProviderException":
      throw await de_DuplicateProviderExceptionRes(parsedOutput, context);
    case "ManagedLoginBrandingExistsException":
    case "com.amazonaws.cognitoidentityprovider#ManagedLoginBrandingExistsException":
      throw await de_ManagedLoginBrandingExistsExceptionRes(parsedOutput, context);
    case "FeatureUnavailableInTierException":
    case "com.amazonaws.cognitoidentityprovider#FeatureUnavailableInTierException":
      throw await de_FeatureUnavailableInTierExceptionRes(parsedOutput, context);
    case "TierChangeNotAllowedException":
    case "com.amazonaws.cognitoidentityprovider#TierChangeNotAllowedException":
      throw await de_TierChangeNotAllowedExceptionRes(parsedOutput, context);
    case "UserPoolTaggingException":
    case "com.amazonaws.cognitoidentityprovider#UserPoolTaggingException":
      throw await de_UserPoolTaggingExceptionRes(parsedOutput, context);
    case "InvalidOAuthFlowException":
    case "com.amazonaws.cognitoidentityprovider#InvalidOAuthFlowException":
      throw await de_InvalidOAuthFlowExceptionRes(parsedOutput, context);
    case "ScopeDoesNotExistException":
    case "com.amazonaws.cognitoidentityprovider#ScopeDoesNotExistException":
      throw await de_ScopeDoesNotExistExceptionRes(parsedOutput, context);
    case "UnsupportedIdentityProviderException":
    case "com.amazonaws.cognitoidentityprovider#UnsupportedIdentityProviderException":
      throw await de_UnsupportedIdentityProviderExceptionRes(parsedOutput, context);
    case "RefreshTokenReuseException":
    case "com.amazonaws.cognitoidentityprovider#RefreshTokenReuseException":
      throw await de_RefreshTokenReuseExceptionRes(parsedOutput, context);
    case "UnauthorizedException":
    case "com.amazonaws.cognitoidentityprovider#UnauthorizedException":
      throw await de_UnauthorizedExceptionRes(parsedOutput, context);
    case "UnsupportedTokenTypeException":
    case "com.amazonaws.cognitoidentityprovider#UnsupportedTokenTypeException":
      throw await de_UnsupportedTokenTypeExceptionRes(parsedOutput, context);
    case "WebAuthnConfigurationMissingException":
    case "com.amazonaws.cognitoidentityprovider#WebAuthnConfigurationMissingException":
      throw await de_WebAuthnConfigurationMissingExceptionRes(parsedOutput, context);
    case "EnableSoftwareTokenMFAException":
    case "com.amazonaws.cognitoidentityprovider#EnableSoftwareTokenMFAException":
      throw await de_EnableSoftwareTokenMFAExceptionRes(parsedOutput, context);
    default:
      const parsedBody = parsedOutput.body;
      return throwDefaultError({
        output,
        parsedBody,
        errorCode
      });
  }
}, "de_CommandError");
var de_AliasExistsExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new AliasExistsException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_AliasExistsExceptionRes");
var de_CodeDeliveryFailureExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new CodeDeliveryFailureException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_CodeDeliveryFailureExceptionRes");
var de_CodeMismatchExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new CodeMismatchException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_CodeMismatchExceptionRes");
var de_ConcurrentModificationExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ConcurrentModificationException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ConcurrentModificationExceptionRes");
var de_DeviceKeyExistsExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new DeviceKeyExistsException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_DeviceKeyExistsExceptionRes");
var de_DuplicateProviderExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new DuplicateProviderException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_DuplicateProviderExceptionRes");
var de_EnableSoftwareTokenMFAExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new EnableSoftwareTokenMFAException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_EnableSoftwareTokenMFAExceptionRes");
var de_ExpiredCodeExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ExpiredCodeException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ExpiredCodeExceptionRes");
var de_FeatureUnavailableInTierExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new FeatureUnavailableInTierException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_FeatureUnavailableInTierExceptionRes");
var de_ForbiddenExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ForbiddenException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ForbiddenExceptionRes");
var de_GroupExistsExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new GroupExistsException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_GroupExistsExceptionRes");
var de_InternalErrorExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InternalErrorException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InternalErrorExceptionRes");
var de_InvalidEmailRoleAccessPolicyExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidEmailRoleAccessPolicyException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidEmailRoleAccessPolicyExceptionRes");
var de_InvalidLambdaResponseExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidLambdaResponseException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidLambdaResponseExceptionRes");
var de_InvalidOAuthFlowExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidOAuthFlowException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidOAuthFlowExceptionRes");
var de_InvalidParameterExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidParameterException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidParameterExceptionRes");
var de_InvalidPasswordExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidPasswordException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidPasswordExceptionRes");
var de_InvalidSmsRoleAccessPolicyExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidSmsRoleAccessPolicyException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidSmsRoleAccessPolicyExceptionRes");
var de_InvalidSmsRoleTrustRelationshipExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidSmsRoleTrustRelationshipException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidSmsRoleTrustRelationshipExceptionRes");
var de_InvalidUserPoolConfigurationExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidUserPoolConfigurationException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidUserPoolConfigurationExceptionRes");
var de_LimitExceededExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new LimitExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_LimitExceededExceptionRes");
var de_ManagedLoginBrandingExistsExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ManagedLoginBrandingExistsException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ManagedLoginBrandingExistsExceptionRes");
var de_MFAMethodNotFoundExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new MFAMethodNotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_MFAMethodNotFoundExceptionRes");
var de_NotAuthorizedExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new NotAuthorizedException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_NotAuthorizedExceptionRes");
var de_PasswordHistoryPolicyViolationExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new PasswordHistoryPolicyViolationException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_PasswordHistoryPolicyViolationExceptionRes");
var de_PasswordResetRequiredExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new PasswordResetRequiredException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_PasswordResetRequiredExceptionRes");
var de_PreconditionNotMetExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new PreconditionNotMetException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_PreconditionNotMetExceptionRes");
var de_RefreshTokenReuseExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new RefreshTokenReuseException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_RefreshTokenReuseExceptionRes");
var de_ResourceNotFoundExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ResourceNotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ResourceNotFoundExceptionRes");
var de_ScopeDoesNotExistExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ScopeDoesNotExistException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ScopeDoesNotExistExceptionRes");
var de_SoftwareTokenMFANotFoundExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new SoftwareTokenMFANotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_SoftwareTokenMFANotFoundExceptionRes");
var de_TierChangeNotAllowedExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new TierChangeNotAllowedException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_TierChangeNotAllowedExceptionRes");
var de_TooManyFailedAttemptsExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new TooManyFailedAttemptsException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_TooManyFailedAttemptsExceptionRes");
var de_TooManyRequestsExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new TooManyRequestsException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_TooManyRequestsExceptionRes");
var de_UnauthorizedExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UnauthorizedException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UnauthorizedExceptionRes");
var de_UnexpectedLambdaExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UnexpectedLambdaException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UnexpectedLambdaExceptionRes");
var de_UnsupportedIdentityProviderExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UnsupportedIdentityProviderException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UnsupportedIdentityProviderExceptionRes");
var de_UnsupportedOperationExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UnsupportedOperationException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UnsupportedOperationExceptionRes");
var de_UnsupportedTokenTypeExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UnsupportedTokenTypeException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UnsupportedTokenTypeExceptionRes");
var de_UnsupportedUserStateExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UnsupportedUserStateException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UnsupportedUserStateExceptionRes");
var de_UserImportInProgressExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UserImportInProgressException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UserImportInProgressExceptionRes");
var de_UserLambdaValidationExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UserLambdaValidationException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UserLambdaValidationExceptionRes");
var de_UsernameExistsExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UsernameExistsException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UsernameExistsExceptionRes");
var de_UserNotConfirmedExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UserNotConfirmedException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UserNotConfirmedExceptionRes");
var de_UserNotFoundExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UserNotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UserNotFoundExceptionRes");
var de_UserPoolAddOnNotEnabledExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UserPoolAddOnNotEnabledException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UserPoolAddOnNotEnabledExceptionRes");
var de_UserPoolTaggingExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UserPoolTaggingException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UserPoolTaggingExceptionRes");
var de_WebAuthnChallengeNotFoundExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new WebAuthnChallengeNotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_WebAuthnChallengeNotFoundExceptionRes");
var de_WebAuthnClientMismatchExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new WebAuthnClientMismatchException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_WebAuthnClientMismatchExceptionRes");
var de_WebAuthnConfigurationMissingExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new WebAuthnConfigurationMissingException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_WebAuthnConfigurationMissingExceptionRes");
var de_WebAuthnCredentialNotSupportedExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new WebAuthnCredentialNotSupportedException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_WebAuthnCredentialNotSupportedExceptionRes");
var de_WebAuthnNotEnabledExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new WebAuthnNotEnabledException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_WebAuthnNotEnabledExceptionRes");
var de_WebAuthnOriginNotAllowedExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new WebAuthnOriginNotAllowedException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_WebAuthnOriginNotAllowedExceptionRes");
var de_WebAuthnRelyingPartyMismatchExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new WebAuthnRelyingPartyMismatchException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_WebAuthnRelyingPartyMismatchExceptionRes");
var se_AssetListType = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_AssetType(entry, context);
  });
}, "se_AssetListType");
var se_AssetType = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Bytes: context.base64Encoder,
    Category: [],
    ColorMode: [],
    Extension: [],
    ResourceId: []
  });
}, "se_AssetType");
var se_CompleteWebAuthnRegistrationRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    AccessToken: [],
    Credential: /* @__PURE__ */ __name((_) => se_Document(_, context), "Credential")
  });
}, "se_CompleteWebAuthnRegistrationRequest");
var se_CreateManagedLoginBrandingRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Assets: /* @__PURE__ */ __name((_) => se_AssetListType(_, context), "Assets"),
    ClientId: [],
    Settings: /* @__PURE__ */ __name((_) => se_Document(_, context), "Settings"),
    UseCognitoProvidedValues: [],
    UserPoolId: []
  });
}, "se_CreateManagedLoginBrandingRequest");
var se_Document = /* @__PURE__ */ __name((input, context) => {
  return input;
}, "se_Document");
var se_SetUICustomizationRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    CSS: [],
    ClientId: [],
    ImageFile: context.base64Encoder,
    UserPoolId: []
  });
}, "se_SetUICustomizationRequest");
var se_UpdateManagedLoginBrandingRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Assets: /* @__PURE__ */ __name((_) => se_AssetListType(_, context), "Assets"),
    ManagedLoginBrandingId: [],
    Settings: /* @__PURE__ */ __name((_) => se_Document(_, context), "Settings"),
    UseCognitoProvidedValues: [],
    UserPoolId: []
  });
}, "se_UpdateManagedLoginBrandingRequest");
var de_AdminCreateUserResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    User: /* @__PURE__ */ __name((_) => de_UserType(_, context), "User")
  });
}, "de_AdminCreateUserResponse");
var de_AdminGetDeviceResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Device: /* @__PURE__ */ __name((_) => de_DeviceType(_, context), "Device")
  });
}, "de_AdminGetDeviceResponse");
var de_AdminGetUserResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Enabled: import_smithy_client.expectBoolean,
    MFAOptions: import_smithy_client._json,
    PreferredMfaSetting: import_smithy_client.expectString,
    UserAttributes: import_smithy_client._json,
    UserCreateDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "UserCreateDate"),
    UserLastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "UserLastModifiedDate"),
    UserMFASettingList: import_smithy_client._json,
    UserStatus: import_smithy_client.expectString,
    Username: import_smithy_client.expectString
  });
}, "de_AdminGetUserResponse");
var de_AdminListDevicesResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Devices: /* @__PURE__ */ __name((_) => de_DeviceListType(_, context), "Devices"),
    PaginationToken: import_smithy_client.expectString
  });
}, "de_AdminListDevicesResponse");
var de_AdminListGroupsForUserResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Groups: /* @__PURE__ */ __name((_) => de_GroupListType(_, context), "Groups"),
    NextToken: import_smithy_client.expectString
  });
}, "de_AdminListGroupsForUserResponse");
var de_AdminListUserAuthEventsResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AuthEvents: /* @__PURE__ */ __name((_) => de_AuthEventsType(_, context), "AuthEvents"),
    NextToken: import_smithy_client.expectString
  });
}, "de_AdminListUserAuthEventsResponse");
var de_AssetListType = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_AssetType(entry, context);
  });
  return retVal;
}, "de_AssetListType");
var de_AssetType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Bytes: context.base64Decoder,
    Category: import_smithy_client.expectString,
    ColorMode: import_smithy_client.expectString,
    Extension: import_smithy_client.expectString,
    ResourceId: import_smithy_client.expectString
  });
}, "de_AssetType");
var de_AuthEventsType = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_AuthEventType(entry, context);
  });
  return retVal;
}, "de_AuthEventsType");
var de_AuthEventType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    ChallengeResponses: import_smithy_client._json,
    CreationDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationDate"),
    EventContextData: import_smithy_client._json,
    EventFeedback: /* @__PURE__ */ __name((_) => de_EventFeedbackType(_, context), "EventFeedback"),
    EventId: import_smithy_client.expectString,
    EventResponse: import_smithy_client.expectString,
    EventRisk: import_smithy_client._json,
    EventType: import_smithy_client.expectString
  });
}, "de_AuthEventType");
var de_CreateGroupResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Group: /* @__PURE__ */ __name((_) => de_GroupType(_, context), "Group")
  });
}, "de_CreateGroupResponse");
var de_CreateIdentityProviderResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    IdentityProvider: /* @__PURE__ */ __name((_) => de_IdentityProviderType(_, context), "IdentityProvider")
  });
}, "de_CreateIdentityProviderResponse");
var de_CreateManagedLoginBrandingResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    ManagedLoginBranding: /* @__PURE__ */ __name((_) => de_ManagedLoginBrandingType(_, context), "ManagedLoginBranding")
  });
}, "de_CreateManagedLoginBrandingResponse");
var de_CreateUserImportJobResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UserImportJob: /* @__PURE__ */ __name((_) => de_UserImportJobType(_, context), "UserImportJob")
  });
}, "de_CreateUserImportJobResponse");
var de_CreateUserPoolClientResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UserPoolClient: /* @__PURE__ */ __name((_) => de_UserPoolClientType(_, context), "UserPoolClient")
  });
}, "de_CreateUserPoolClientResponse");
var de_CreateUserPoolResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UserPool: /* @__PURE__ */ __name((_) => de_UserPoolType(_, context), "UserPool")
  });
}, "de_CreateUserPoolResponse");
var de_DescribeIdentityProviderResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    IdentityProvider: /* @__PURE__ */ __name((_) => de_IdentityProviderType(_, context), "IdentityProvider")
  });
}, "de_DescribeIdentityProviderResponse");
var de_DescribeManagedLoginBrandingByClientResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    ManagedLoginBranding: /* @__PURE__ */ __name((_) => de_ManagedLoginBrandingType(_, context), "ManagedLoginBranding")
  });
}, "de_DescribeManagedLoginBrandingByClientResponse");
var de_DescribeManagedLoginBrandingResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    ManagedLoginBranding: /* @__PURE__ */ __name((_) => de_ManagedLoginBrandingType(_, context), "ManagedLoginBranding")
  });
}, "de_DescribeManagedLoginBrandingResponse");
var de_DescribeRiskConfigurationResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    RiskConfiguration: /* @__PURE__ */ __name((_) => de_RiskConfigurationType(_, context), "RiskConfiguration")
  });
}, "de_DescribeRiskConfigurationResponse");
var de_DescribeUserImportJobResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UserImportJob: /* @__PURE__ */ __name((_) => de_UserImportJobType(_, context), "UserImportJob")
  });
}, "de_DescribeUserImportJobResponse");
var de_DescribeUserPoolClientResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UserPoolClient: /* @__PURE__ */ __name((_) => de_UserPoolClientType(_, context), "UserPoolClient")
  });
}, "de_DescribeUserPoolClientResponse");
var de_DescribeUserPoolResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UserPool: /* @__PURE__ */ __name((_) => de_UserPoolType(_, context), "UserPool")
  });
}, "de_DescribeUserPoolResponse");
var de_DeviceListType = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_DeviceType(entry, context);
  });
  return retVal;
}, "de_DeviceListType");
var de_DeviceType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    DeviceAttributes: import_smithy_client._json,
    DeviceCreateDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "DeviceCreateDate"),
    DeviceKey: import_smithy_client.expectString,
    DeviceLastAuthenticatedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "DeviceLastAuthenticatedDate"),
    DeviceLastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "DeviceLastModifiedDate")
  });
}, "de_DeviceType");
var de_Document = /* @__PURE__ */ __name((output, context) => {
  return output;
}, "de_Document");
var de_EventFeedbackType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    FeedbackDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "FeedbackDate"),
    FeedbackValue: import_smithy_client.expectString,
    Provider: import_smithy_client.expectString
  });
}, "de_EventFeedbackType");
var de_GetDeviceResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Device: /* @__PURE__ */ __name((_) => de_DeviceType(_, context), "Device")
  });
}, "de_GetDeviceResponse");
var de_GetGroupResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Group: /* @__PURE__ */ __name((_) => de_GroupType(_, context), "Group")
  });
}, "de_GetGroupResponse");
var de_GetIdentityProviderByIdentifierResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    IdentityProvider: /* @__PURE__ */ __name((_) => de_IdentityProviderType(_, context), "IdentityProvider")
  });
}, "de_GetIdentityProviderByIdentifierResponse");
var de_GetUICustomizationResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UICustomization: /* @__PURE__ */ __name((_) => de_UICustomizationType(_, context), "UICustomization")
  });
}, "de_GetUICustomizationResponse");
var de_GroupListType = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_GroupType(entry, context);
  });
  return retVal;
}, "de_GroupListType");
var de_GroupType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    CreationDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationDate"),
    Description: import_smithy_client.expectString,
    GroupName: import_smithy_client.expectString,
    LastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "LastModifiedDate"),
    Precedence: import_smithy_client.expectInt32,
    RoleArn: import_smithy_client.expectString,
    UserPoolId: import_smithy_client.expectString
  });
}, "de_GroupType");
var de_IdentityProviderType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AttributeMapping: import_smithy_client._json,
    CreationDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationDate"),
    IdpIdentifiers: import_smithy_client._json,
    LastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "LastModifiedDate"),
    ProviderDetails: import_smithy_client._json,
    ProviderName: import_smithy_client.expectString,
    ProviderType: import_smithy_client.expectString,
    UserPoolId: import_smithy_client.expectString
  });
}, "de_IdentityProviderType");
var de_ListDevicesResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Devices: /* @__PURE__ */ __name((_) => de_DeviceListType(_, context), "Devices"),
    PaginationToken: import_smithy_client.expectString
  });
}, "de_ListDevicesResponse");
var de_ListGroupsResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Groups: /* @__PURE__ */ __name((_) => de_GroupListType(_, context), "Groups"),
    NextToken: import_smithy_client.expectString
  });
}, "de_ListGroupsResponse");
var de_ListIdentityProvidersResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    NextToken: import_smithy_client.expectString,
    Providers: /* @__PURE__ */ __name((_) => de_ProvidersListType(_, context), "Providers")
  });
}, "de_ListIdentityProvidersResponse");
var de_ListUserImportJobsResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    PaginationToken: import_smithy_client.expectString,
    UserImportJobs: /* @__PURE__ */ __name((_) => de_UserImportJobsListType(_, context), "UserImportJobs")
  });
}, "de_ListUserImportJobsResponse");
var de_ListUserPoolsResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    NextToken: import_smithy_client.expectString,
    UserPools: /* @__PURE__ */ __name((_) => de_UserPoolListType(_, context), "UserPools")
  });
}, "de_ListUserPoolsResponse");
var de_ListUsersInGroupResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    NextToken: import_smithy_client.expectString,
    Users: /* @__PURE__ */ __name((_) => de_UsersListType(_, context), "Users")
  });
}, "de_ListUsersInGroupResponse");
var de_ListUsersResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    PaginationToken: import_smithy_client.expectString,
    Users: /* @__PURE__ */ __name((_) => de_UsersListType(_, context), "Users")
  });
}, "de_ListUsersResponse");
var de_ListWebAuthnCredentialsResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Credentials: /* @__PURE__ */ __name((_) => de_WebAuthnCredentialDescriptionListType(_, context), "Credentials"),
    NextToken: import_smithy_client.expectString
  });
}, "de_ListWebAuthnCredentialsResponse");
var de_ManagedLoginBrandingType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Assets: /* @__PURE__ */ __name((_) => de_AssetListType(_, context), "Assets"),
    CreationDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationDate"),
    LastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "LastModifiedDate"),
    ManagedLoginBrandingId: import_smithy_client.expectString,
    Settings: /* @__PURE__ */ __name((_) => de_Document(_, context), "Settings"),
    UseCognitoProvidedValues: import_smithy_client.expectBoolean,
    UserPoolId: import_smithy_client.expectString
  });
}, "de_ManagedLoginBrandingType");
var de_ProviderDescription = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    CreationDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationDate"),
    LastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "LastModifiedDate"),
    ProviderName: import_smithy_client.expectString,
    ProviderType: import_smithy_client.expectString
  });
}, "de_ProviderDescription");
var de_ProvidersListType = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ProviderDescription(entry, context);
  });
  return retVal;
}, "de_ProvidersListType");
var de_RiskConfigurationType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AccountTakeoverRiskConfiguration: import_smithy_client._json,
    ClientId: import_smithy_client.expectString,
    CompromisedCredentialsRiskConfiguration: import_smithy_client._json,
    LastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "LastModifiedDate"),
    RiskExceptionConfiguration: import_smithy_client._json,
    UserPoolId: import_smithy_client.expectString
  });
}, "de_RiskConfigurationType");
var de_SetRiskConfigurationResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    RiskConfiguration: /* @__PURE__ */ __name((_) => de_RiskConfigurationType(_, context), "RiskConfiguration")
  });
}, "de_SetRiskConfigurationResponse");
var de_SetUICustomizationResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UICustomization: /* @__PURE__ */ __name((_) => de_UICustomizationType(_, context), "UICustomization")
  });
}, "de_SetUICustomizationResponse");
var de_StartUserImportJobResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UserImportJob: /* @__PURE__ */ __name((_) => de_UserImportJobType(_, context), "UserImportJob")
  });
}, "de_StartUserImportJobResponse");
var de_StartWebAuthnRegistrationResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    CredentialCreationOptions: /* @__PURE__ */ __name((_) => de_Document(_, context), "CredentialCreationOptions")
  });
}, "de_StartWebAuthnRegistrationResponse");
var de_StopUserImportJobResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UserImportJob: /* @__PURE__ */ __name((_) => de_UserImportJobType(_, context), "UserImportJob")
  });
}, "de_StopUserImportJobResponse");
var de_UICustomizationType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    CSS: import_smithy_client.expectString,
    CSSVersion: import_smithy_client.expectString,
    ClientId: import_smithy_client.expectString,
    CreationDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationDate"),
    ImageUrl: import_smithy_client.expectString,
    LastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "LastModifiedDate"),
    UserPoolId: import_smithy_client.expectString
  });
}, "de_UICustomizationType");
var de_UpdateGroupResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Group: /* @__PURE__ */ __name((_) => de_GroupType(_, context), "Group")
  });
}, "de_UpdateGroupResponse");
var de_UpdateIdentityProviderResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    IdentityProvider: /* @__PURE__ */ __name((_) => de_IdentityProviderType(_, context), "IdentityProvider")
  });
}, "de_UpdateIdentityProviderResponse");
var de_UpdateManagedLoginBrandingResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    ManagedLoginBranding: /* @__PURE__ */ __name((_) => de_ManagedLoginBrandingType(_, context), "ManagedLoginBranding")
  });
}, "de_UpdateManagedLoginBrandingResponse");
var de_UpdateUserPoolClientResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    UserPoolClient: /* @__PURE__ */ __name((_) => de_UserPoolClientType(_, context), "UserPoolClient")
  });
}, "de_UpdateUserPoolClientResponse");
var de_UserImportJobsListType = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_UserImportJobType(entry, context);
  });
  return retVal;
}, "de_UserImportJobsListType");
var de_UserImportJobType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    CloudWatchLogsRoleArn: import_smithy_client.expectString,
    CompletionDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CompletionDate"),
    CompletionMessage: import_smithy_client.expectString,
    CreationDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationDate"),
    FailedUsers: import_smithy_client.expectLong,
    ImportedUsers: import_smithy_client.expectLong,
    JobId: import_smithy_client.expectString,
    JobName: import_smithy_client.expectString,
    PreSignedUrl: import_smithy_client.expectString,
    SkippedUsers: import_smithy_client.expectLong,
    StartDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "StartDate"),
    Status: import_smithy_client.expectString,
    UserPoolId: import_smithy_client.expectString
  });
}, "de_UserImportJobType");
var de_UserPoolClientType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AccessTokenValidity: import_smithy_client.expectInt32,
    AllowedOAuthFlows: import_smithy_client._json,
    AllowedOAuthFlowsUserPoolClient: import_smithy_client.expectBoolean,
    AllowedOAuthScopes: import_smithy_client._json,
    AnalyticsConfiguration: import_smithy_client._json,
    AuthSessionValidity: import_smithy_client.expectInt32,
    CallbackURLs: import_smithy_client._json,
    ClientId: import_smithy_client.expectString,
    ClientName: import_smithy_client.expectString,
    ClientSecret: import_smithy_client.expectString,
    CreationDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationDate"),
    DefaultRedirectURI: import_smithy_client.expectString,
    EnablePropagateAdditionalUserContextData: import_smithy_client.expectBoolean,
    EnableTokenRevocation: import_smithy_client.expectBoolean,
    ExplicitAuthFlows: import_smithy_client._json,
    IdTokenValidity: import_smithy_client.expectInt32,
    LastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "LastModifiedDate"),
    LogoutURLs: import_smithy_client._json,
    PreventUserExistenceErrors: import_smithy_client.expectString,
    ReadAttributes: import_smithy_client._json,
    RefreshTokenRotation: import_smithy_client._json,
    RefreshTokenValidity: import_smithy_client.expectInt32,
    SupportedIdentityProviders: import_smithy_client._json,
    TokenValidityUnits: import_smithy_client._json,
    UserPoolId: import_smithy_client.expectString,
    WriteAttributes: import_smithy_client._json
  });
}, "de_UserPoolClientType");
var de_UserPoolDescriptionType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    CreationDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationDate"),
    Id: import_smithy_client.expectString,
    LambdaConfig: import_smithy_client._json,
    LastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "LastModifiedDate"),
    Name: import_smithy_client.expectString,
    Status: import_smithy_client.expectString
  });
}, "de_UserPoolDescriptionType");
var de_UserPoolListType = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_UserPoolDescriptionType(entry, context);
  });
  return retVal;
}, "de_UserPoolListType");
var de_UserPoolType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AccountRecoverySetting: import_smithy_client._json,
    AdminCreateUserConfig: import_smithy_client._json,
    AliasAttributes: import_smithy_client._json,
    Arn: import_smithy_client.expectString,
    AutoVerifiedAttributes: import_smithy_client._json,
    CreationDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationDate"),
    CustomDomain: import_smithy_client.expectString,
    DeletionProtection: import_smithy_client.expectString,
    DeviceConfiguration: import_smithy_client._json,
    Domain: import_smithy_client.expectString,
    EmailConfiguration: import_smithy_client._json,
    EmailConfigurationFailure: import_smithy_client.expectString,
    EmailVerificationMessage: import_smithy_client.expectString,
    EmailVerificationSubject: import_smithy_client.expectString,
    EstimatedNumberOfUsers: import_smithy_client.expectInt32,
    Id: import_smithy_client.expectString,
    LambdaConfig: import_smithy_client._json,
    LastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "LastModifiedDate"),
    MfaConfiguration: import_smithy_client.expectString,
    Name: import_smithy_client.expectString,
    Policies: import_smithy_client._json,
    SchemaAttributes: import_smithy_client._json,
    SmsAuthenticationMessage: import_smithy_client.expectString,
    SmsConfiguration: import_smithy_client._json,
    SmsConfigurationFailure: import_smithy_client.expectString,
    SmsVerificationMessage: import_smithy_client.expectString,
    Status: import_smithy_client.expectString,
    UserAttributeUpdateSettings: import_smithy_client._json,
    UserPoolAddOns: import_smithy_client._json,
    UserPoolTags: import_smithy_client._json,
    UserPoolTier: import_smithy_client.expectString,
    UsernameAttributes: import_smithy_client._json,
    UsernameConfiguration: import_smithy_client._json,
    VerificationMessageTemplate: import_smithy_client._json
  });
}, "de_UserPoolType");
var de_UsersListType = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_UserType(entry, context);
  });
  return retVal;
}, "de_UsersListType");
var de_UserType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Attributes: import_smithy_client._json,
    Enabled: import_smithy_client.expectBoolean,
    MFAOptions: import_smithy_client._json,
    UserCreateDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "UserCreateDate"),
    UserLastModifiedDate: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "UserLastModifiedDate"),
    UserStatus: import_smithy_client.expectString,
    Username: import_smithy_client.expectString
  });
}, "de_UserType");
var de_WebAuthnCredentialDescription = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AuthenticatorAttachment: import_smithy_client.expectString,
    AuthenticatorTransports: import_smithy_client._json,
    CreatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreatedAt"),
    CredentialId: import_smithy_client.expectString,
    FriendlyCredentialName: import_smithy_client.expectString,
    RelyingPartyId: import_smithy_client.expectString
  });
}, "de_WebAuthnCredentialDescription");
var de_WebAuthnCredentialDescriptionListType = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_WebAuthnCredentialDescription(entry, context);
  });
  return retVal;
}, "de_WebAuthnCredentialDescriptionListType");
var deserializeMetadata = /* @__PURE__ */ __name((output) => ({
  httpStatusCode: output.statusCode,
  requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
  extendedRequestId: output.headers["x-amz-id-2"],
  cfId: output.headers["x-amz-cf-id"]
}), "deserializeMetadata");
var throwDefaultError = (0, import_smithy_client.withBaseException)(CognitoIdentityProviderServiceException);
var buildHttpRpcRequest = /* @__PURE__ */ __name(async (context, headers, path, resolvedHostname, body) => {
  const { hostname, protocol = "https", port, path: basePath } = await context.endpoint();
  const contents = {
    protocol,
    hostname,
    port,
    method: "POST",
    path: basePath.endsWith("/") ? basePath.slice(0, -1) + path : basePath + path,
    headers
  };
  if (resolvedHostname !== void 0) {
    contents.hostname = resolvedHostname;
  }
  if (body !== void 0) {
    contents.body = body;
  }
  return new import_protocol_http.HttpRequest(contents);
}, "buildHttpRpcRequest");
function sharedHeaders(operation) {
  return {
    "content-type": "application/x-amz-json-1.1",
    "x-amz-target": `AWSCognitoIdentityProviderService.${operation}`
  };
}
__name(sharedHeaders, "sharedHeaders");

// src/commands/AddCustomAttributesCommand.ts
var AddCustomAttributesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AddCustomAttributes", {}).n("CognitoIdentityProviderClient", "AddCustomAttributesCommand").f(void 0, void 0).ser(se_AddCustomAttributesCommand).de(de_AddCustomAttributesCommand).build() {
  static {
    __name(this, "AddCustomAttributesCommand");
  }
};

// src/commands/AdminAddUserToGroupCommand.ts



var AdminAddUserToGroupCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminAddUserToGroup", {}).n("CognitoIdentityProviderClient", "AdminAddUserToGroupCommand").f(AdminAddUserToGroupRequestFilterSensitiveLog, void 0).ser(se_AdminAddUserToGroupCommand).de(de_AdminAddUserToGroupCommand).build() {
  static {
    __name(this, "AdminAddUserToGroupCommand");
  }
};

// src/commands/AdminConfirmSignUpCommand.ts



var AdminConfirmSignUpCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminConfirmSignUp", {}).n("CognitoIdentityProviderClient", "AdminConfirmSignUpCommand").f(AdminConfirmSignUpRequestFilterSensitiveLog, void 0).ser(se_AdminConfirmSignUpCommand).de(de_AdminConfirmSignUpCommand).build() {
  static {
    __name(this, "AdminConfirmSignUpCommand");
  }
};

// src/commands/AdminCreateUserCommand.ts



var AdminCreateUserCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminCreateUser", {}).n("CognitoIdentityProviderClient", "AdminCreateUserCommand").f(AdminCreateUserRequestFilterSensitiveLog, AdminCreateUserResponseFilterSensitiveLog).ser(se_AdminCreateUserCommand).de(de_AdminCreateUserCommand).build() {
  static {
    __name(this, "AdminCreateUserCommand");
  }
};

// src/commands/AdminDeleteUserAttributesCommand.ts



var AdminDeleteUserAttributesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminDeleteUserAttributes", {}).n("CognitoIdentityProviderClient", "AdminDeleteUserAttributesCommand").f(AdminDeleteUserAttributesRequestFilterSensitiveLog, void 0).ser(se_AdminDeleteUserAttributesCommand).de(de_AdminDeleteUserAttributesCommand).build() {
  static {
    __name(this, "AdminDeleteUserAttributesCommand");
  }
};

// src/commands/AdminDeleteUserCommand.ts



var AdminDeleteUserCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminDeleteUser", {}).n("CognitoIdentityProviderClient", "AdminDeleteUserCommand").f(AdminDeleteUserRequestFilterSensitiveLog, void 0).ser(se_AdminDeleteUserCommand).de(de_AdminDeleteUserCommand).build() {
  static {
    __name(this, "AdminDeleteUserCommand");
  }
};

// src/commands/AdminDisableProviderForUserCommand.ts



var AdminDisableProviderForUserCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminDisableProviderForUser", {}).n("CognitoIdentityProviderClient", "AdminDisableProviderForUserCommand").f(void 0, void 0).ser(se_AdminDisableProviderForUserCommand).de(de_AdminDisableProviderForUserCommand).build() {
  static {
    __name(this, "AdminDisableProviderForUserCommand");
  }
};

// src/commands/AdminDisableUserCommand.ts



var AdminDisableUserCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminDisableUser", {}).n("CognitoIdentityProviderClient", "AdminDisableUserCommand").f(AdminDisableUserRequestFilterSensitiveLog, void 0).ser(se_AdminDisableUserCommand).de(de_AdminDisableUserCommand).build() {
  static {
    __name(this, "AdminDisableUserCommand");
  }
};

// src/commands/AdminEnableUserCommand.ts



var AdminEnableUserCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminEnableUser", {}).n("CognitoIdentityProviderClient", "AdminEnableUserCommand").f(AdminEnableUserRequestFilterSensitiveLog, void 0).ser(se_AdminEnableUserCommand).de(de_AdminEnableUserCommand).build() {
  static {
    __name(this, "AdminEnableUserCommand");
  }
};

// src/commands/AdminForgetDeviceCommand.ts



var AdminForgetDeviceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminForgetDevice", {}).n("CognitoIdentityProviderClient", "AdminForgetDeviceCommand").f(AdminForgetDeviceRequestFilterSensitiveLog, void 0).ser(se_AdminForgetDeviceCommand).de(de_AdminForgetDeviceCommand).build() {
  static {
    __name(this, "AdminForgetDeviceCommand");
  }
};

// src/commands/AdminGetDeviceCommand.ts



var AdminGetDeviceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminGetDevice", {}).n("CognitoIdentityProviderClient", "AdminGetDeviceCommand").f(AdminGetDeviceRequestFilterSensitiveLog, AdminGetDeviceResponseFilterSensitiveLog).ser(se_AdminGetDeviceCommand).de(de_AdminGetDeviceCommand).build() {
  static {
    __name(this, "AdminGetDeviceCommand");
  }
};

// src/commands/AdminGetUserCommand.ts



var AdminGetUserCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminGetUser", {}).n("CognitoIdentityProviderClient", "AdminGetUserCommand").f(AdminGetUserRequestFilterSensitiveLog, AdminGetUserResponseFilterSensitiveLog).ser(se_AdminGetUserCommand).de(de_AdminGetUserCommand).build() {
  static {
    __name(this, "AdminGetUserCommand");
  }
};

// src/commands/AdminInitiateAuthCommand.ts



var AdminInitiateAuthCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminInitiateAuth", {}).n("CognitoIdentityProviderClient", "AdminInitiateAuthCommand").f(AdminInitiateAuthRequestFilterSensitiveLog, AdminInitiateAuthResponseFilterSensitiveLog).ser(se_AdminInitiateAuthCommand).de(de_AdminInitiateAuthCommand).build() {
  static {
    __name(this, "AdminInitiateAuthCommand");
  }
};

// src/commands/AdminLinkProviderForUserCommand.ts



var AdminLinkProviderForUserCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminLinkProviderForUser", {}).n("CognitoIdentityProviderClient", "AdminLinkProviderForUserCommand").f(void 0, void 0).ser(se_AdminLinkProviderForUserCommand).de(de_AdminLinkProviderForUserCommand).build() {
  static {
    __name(this, "AdminLinkProviderForUserCommand");
  }
};

// src/commands/AdminListDevicesCommand.ts



var AdminListDevicesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminListDevices", {}).n("CognitoIdentityProviderClient", "AdminListDevicesCommand").f(AdminListDevicesRequestFilterSensitiveLog, AdminListDevicesResponseFilterSensitiveLog).ser(se_AdminListDevicesCommand).de(de_AdminListDevicesCommand).build() {
  static {
    __name(this, "AdminListDevicesCommand");
  }
};

// src/commands/AdminListGroupsForUserCommand.ts



var AdminListGroupsForUserCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminListGroupsForUser", {}).n("CognitoIdentityProviderClient", "AdminListGroupsForUserCommand").f(AdminListGroupsForUserRequestFilterSensitiveLog, void 0).ser(se_AdminListGroupsForUserCommand).de(de_AdminListGroupsForUserCommand).build() {
  static {
    __name(this, "AdminListGroupsForUserCommand");
  }
};

// src/commands/AdminListUserAuthEventsCommand.ts



var AdminListUserAuthEventsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminListUserAuthEvents", {}).n("CognitoIdentityProviderClient", "AdminListUserAuthEventsCommand").f(AdminListUserAuthEventsRequestFilterSensitiveLog, void 0).ser(se_AdminListUserAuthEventsCommand).de(de_AdminListUserAuthEventsCommand).build() {
  static {
    __name(this, "AdminListUserAuthEventsCommand");
  }
};

// src/commands/AdminRemoveUserFromGroupCommand.ts



var AdminRemoveUserFromGroupCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminRemoveUserFromGroup", {}).n("CognitoIdentityProviderClient", "AdminRemoveUserFromGroupCommand").f(AdminRemoveUserFromGroupRequestFilterSensitiveLog, void 0).ser(se_AdminRemoveUserFromGroupCommand).de(de_AdminRemoveUserFromGroupCommand).build() {
  static {
    __name(this, "AdminRemoveUserFromGroupCommand");
  }
};

// src/commands/AdminResetUserPasswordCommand.ts



var AdminResetUserPasswordCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminResetUserPassword", {}).n("CognitoIdentityProviderClient", "AdminResetUserPasswordCommand").f(AdminResetUserPasswordRequestFilterSensitiveLog, void 0).ser(se_AdminResetUserPasswordCommand).de(de_AdminResetUserPasswordCommand).build() {
  static {
    __name(this, "AdminResetUserPasswordCommand");
  }
};

// src/commands/AdminRespondToAuthChallengeCommand.ts



var AdminRespondToAuthChallengeCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminRespondToAuthChallenge", {}).n("CognitoIdentityProviderClient", "AdminRespondToAuthChallengeCommand").f(AdminRespondToAuthChallengeRequestFilterSensitiveLog, AdminRespondToAuthChallengeResponseFilterSensitiveLog).ser(se_AdminRespondToAuthChallengeCommand).de(de_AdminRespondToAuthChallengeCommand).build() {
  static {
    __name(this, "AdminRespondToAuthChallengeCommand");
  }
};

// src/commands/AdminSetUserMFAPreferenceCommand.ts



var AdminSetUserMFAPreferenceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminSetUserMFAPreference", {}).n("CognitoIdentityProviderClient", "AdminSetUserMFAPreferenceCommand").f(AdminSetUserMFAPreferenceRequestFilterSensitiveLog, void 0).ser(se_AdminSetUserMFAPreferenceCommand).de(de_AdminSetUserMFAPreferenceCommand).build() {
  static {
    __name(this, "AdminSetUserMFAPreferenceCommand");
  }
};

// src/commands/AdminSetUserPasswordCommand.ts



var AdminSetUserPasswordCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminSetUserPassword", {}).n("CognitoIdentityProviderClient", "AdminSetUserPasswordCommand").f(AdminSetUserPasswordRequestFilterSensitiveLog, void 0).ser(se_AdminSetUserPasswordCommand).de(de_AdminSetUserPasswordCommand).build() {
  static {
    __name(this, "AdminSetUserPasswordCommand");
  }
};

// src/commands/AdminSetUserSettingsCommand.ts



var AdminSetUserSettingsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminSetUserSettings", {}).n("CognitoIdentityProviderClient", "AdminSetUserSettingsCommand").f(AdminSetUserSettingsRequestFilterSensitiveLog, void 0).ser(se_AdminSetUserSettingsCommand).de(de_AdminSetUserSettingsCommand).build() {
  static {
    __name(this, "AdminSetUserSettingsCommand");
  }
};

// src/commands/AdminUpdateAuthEventFeedbackCommand.ts



var AdminUpdateAuthEventFeedbackCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminUpdateAuthEventFeedback", {}).n("CognitoIdentityProviderClient", "AdminUpdateAuthEventFeedbackCommand").f(AdminUpdateAuthEventFeedbackRequestFilterSensitiveLog, void 0).ser(se_AdminUpdateAuthEventFeedbackCommand).de(de_AdminUpdateAuthEventFeedbackCommand).build() {
  static {
    __name(this, "AdminUpdateAuthEventFeedbackCommand");
  }
};

// src/commands/AdminUpdateDeviceStatusCommand.ts



var AdminUpdateDeviceStatusCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminUpdateDeviceStatus", {}).n("CognitoIdentityProviderClient", "AdminUpdateDeviceStatusCommand").f(AdminUpdateDeviceStatusRequestFilterSensitiveLog, void 0).ser(se_AdminUpdateDeviceStatusCommand).de(de_AdminUpdateDeviceStatusCommand).build() {
  static {
    __name(this, "AdminUpdateDeviceStatusCommand");
  }
};

// src/commands/AdminUpdateUserAttributesCommand.ts



var AdminUpdateUserAttributesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminUpdateUserAttributes", {}).n("CognitoIdentityProviderClient", "AdminUpdateUserAttributesCommand").f(AdminUpdateUserAttributesRequestFilterSensitiveLog, void 0).ser(se_AdminUpdateUserAttributesCommand).de(de_AdminUpdateUserAttributesCommand).build() {
  static {
    __name(this, "AdminUpdateUserAttributesCommand");
  }
};

// src/commands/AdminUserGlobalSignOutCommand.ts



var AdminUserGlobalSignOutCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AdminUserGlobalSignOut", {}).n("CognitoIdentityProviderClient", "AdminUserGlobalSignOutCommand").f(AdminUserGlobalSignOutRequestFilterSensitiveLog, void 0).ser(se_AdminUserGlobalSignOutCommand).de(de_AdminUserGlobalSignOutCommand).build() {
  static {
    __name(this, "AdminUserGlobalSignOutCommand");
  }
};

// src/commands/AssociateSoftwareTokenCommand.ts



var AssociateSoftwareTokenCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "AssociateSoftwareToken", {}).n("CognitoIdentityProviderClient", "AssociateSoftwareTokenCommand").f(AssociateSoftwareTokenRequestFilterSensitiveLog, AssociateSoftwareTokenResponseFilterSensitiveLog).ser(se_AssociateSoftwareTokenCommand).de(de_AssociateSoftwareTokenCommand).build() {
  static {
    __name(this, "AssociateSoftwareTokenCommand");
  }
};

// src/commands/ChangePasswordCommand.ts



var ChangePasswordCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ChangePassword", {}).n("CognitoIdentityProviderClient", "ChangePasswordCommand").f(ChangePasswordRequestFilterSensitiveLog, void 0).ser(se_ChangePasswordCommand).de(de_ChangePasswordCommand).build() {
  static {
    __name(this, "ChangePasswordCommand");
  }
};

// src/commands/CompleteWebAuthnRegistrationCommand.ts



var CompleteWebAuthnRegistrationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "CompleteWebAuthnRegistration", {}).n("CognitoIdentityProviderClient", "CompleteWebAuthnRegistrationCommand").f(CompleteWebAuthnRegistrationRequestFilterSensitiveLog, void 0).ser(se_CompleteWebAuthnRegistrationCommand).de(de_CompleteWebAuthnRegistrationCommand).build() {
  static {
    __name(this, "CompleteWebAuthnRegistrationCommand");
  }
};

// src/commands/ConfirmDeviceCommand.ts



var ConfirmDeviceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ConfirmDevice", {}).n("CognitoIdentityProviderClient", "ConfirmDeviceCommand").f(ConfirmDeviceRequestFilterSensitiveLog, void 0).ser(se_ConfirmDeviceCommand).de(de_ConfirmDeviceCommand).build() {
  static {
    __name(this, "ConfirmDeviceCommand");
  }
};

// src/commands/ConfirmForgotPasswordCommand.ts



var ConfirmForgotPasswordCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ConfirmForgotPassword", {}).n("CognitoIdentityProviderClient", "ConfirmForgotPasswordCommand").f(ConfirmForgotPasswordRequestFilterSensitiveLog, void 0).ser(se_ConfirmForgotPasswordCommand).de(de_ConfirmForgotPasswordCommand).build() {
  static {
    __name(this, "ConfirmForgotPasswordCommand");
  }
};

// src/commands/ConfirmSignUpCommand.ts



var ConfirmSignUpCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ConfirmSignUp", {}).n("CognitoIdentityProviderClient", "ConfirmSignUpCommand").f(ConfirmSignUpRequestFilterSensitiveLog, ConfirmSignUpResponseFilterSensitiveLog).ser(se_ConfirmSignUpCommand).de(de_ConfirmSignUpCommand).build() {
  static {
    __name(this, "ConfirmSignUpCommand");
  }
};

// src/commands/CreateGroupCommand.ts



var CreateGroupCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "CreateGroup", {}).n("CognitoIdentityProviderClient", "CreateGroupCommand").f(void 0, void 0).ser(se_CreateGroupCommand).de(de_CreateGroupCommand).build() {
  static {
    __name(this, "CreateGroupCommand");
  }
};

// src/commands/CreateIdentityProviderCommand.ts



var CreateIdentityProviderCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "CreateIdentityProvider", {}).n("CognitoIdentityProviderClient", "CreateIdentityProviderCommand").f(void 0, void 0).ser(se_CreateIdentityProviderCommand).de(de_CreateIdentityProviderCommand).build() {
  static {
    __name(this, "CreateIdentityProviderCommand");
  }
};

// src/commands/CreateManagedLoginBrandingCommand.ts



var CreateManagedLoginBrandingCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "CreateManagedLoginBranding", {}).n("CognitoIdentityProviderClient", "CreateManagedLoginBrandingCommand").f(CreateManagedLoginBrandingRequestFilterSensitiveLog, void 0).ser(se_CreateManagedLoginBrandingCommand).de(de_CreateManagedLoginBrandingCommand).build() {
  static {
    __name(this, "CreateManagedLoginBrandingCommand");
  }
};

// src/commands/CreateResourceServerCommand.ts



var CreateResourceServerCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "CreateResourceServer", {}).n("CognitoIdentityProviderClient", "CreateResourceServerCommand").f(void 0, void 0).ser(se_CreateResourceServerCommand).de(de_CreateResourceServerCommand).build() {
  static {
    __name(this, "CreateResourceServerCommand");
  }
};

// src/commands/CreateUserImportJobCommand.ts



var CreateUserImportJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "CreateUserImportJob", {}).n("CognitoIdentityProviderClient", "CreateUserImportJobCommand").f(void 0, void 0).ser(se_CreateUserImportJobCommand).de(de_CreateUserImportJobCommand).build() {
  static {
    __name(this, "CreateUserImportJobCommand");
  }
};

// src/commands/CreateUserPoolClientCommand.ts



var CreateUserPoolClientCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "CreateUserPoolClient", {}).n("CognitoIdentityProviderClient", "CreateUserPoolClientCommand").f(void 0, CreateUserPoolClientResponseFilterSensitiveLog).ser(se_CreateUserPoolClientCommand).de(de_CreateUserPoolClientCommand).build() {
  static {
    __name(this, "CreateUserPoolClientCommand");
  }
};

// src/commands/CreateUserPoolCommand.ts



var CreateUserPoolCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "CreateUserPool", {}).n("CognitoIdentityProviderClient", "CreateUserPoolCommand").f(void 0, void 0).ser(se_CreateUserPoolCommand).de(de_CreateUserPoolCommand).build() {
  static {
    __name(this, "CreateUserPoolCommand");
  }
};

// src/commands/CreateUserPoolDomainCommand.ts



var CreateUserPoolDomainCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "CreateUserPoolDomain", {}).n("CognitoIdentityProviderClient", "CreateUserPoolDomainCommand").f(void 0, void 0).ser(se_CreateUserPoolDomainCommand).de(de_CreateUserPoolDomainCommand).build() {
  static {
    __name(this, "CreateUserPoolDomainCommand");
  }
};

// src/commands/DeleteGroupCommand.ts



var DeleteGroupCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DeleteGroup", {}).n("CognitoIdentityProviderClient", "DeleteGroupCommand").f(void 0, void 0).ser(se_DeleteGroupCommand).de(de_DeleteGroupCommand).build() {
  static {
    __name(this, "DeleteGroupCommand");
  }
};

// src/commands/DeleteIdentityProviderCommand.ts



var DeleteIdentityProviderCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DeleteIdentityProvider", {}).n("CognitoIdentityProviderClient", "DeleteIdentityProviderCommand").f(void 0, void 0).ser(se_DeleteIdentityProviderCommand).de(de_DeleteIdentityProviderCommand).build() {
  static {
    __name(this, "DeleteIdentityProviderCommand");
  }
};

// src/commands/DeleteManagedLoginBrandingCommand.ts



var DeleteManagedLoginBrandingCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DeleteManagedLoginBranding", {}).n("CognitoIdentityProviderClient", "DeleteManagedLoginBrandingCommand").f(void 0, void 0).ser(se_DeleteManagedLoginBrandingCommand).de(de_DeleteManagedLoginBrandingCommand).build() {
  static {
    __name(this, "DeleteManagedLoginBrandingCommand");
  }
};

// src/commands/DeleteResourceServerCommand.ts



var DeleteResourceServerCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DeleteResourceServer", {}).n("CognitoIdentityProviderClient", "DeleteResourceServerCommand").f(void 0, void 0).ser(se_DeleteResourceServerCommand).de(de_DeleteResourceServerCommand).build() {
  static {
    __name(this, "DeleteResourceServerCommand");
  }
};

// src/commands/DeleteUserAttributesCommand.ts



var DeleteUserAttributesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DeleteUserAttributes", {}).n("CognitoIdentityProviderClient", "DeleteUserAttributesCommand").f(DeleteUserAttributesRequestFilterSensitiveLog, void 0).ser(se_DeleteUserAttributesCommand).de(de_DeleteUserAttributesCommand).build() {
  static {
    __name(this, "DeleteUserAttributesCommand");
  }
};

// src/commands/DeleteUserCommand.ts



var DeleteUserCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DeleteUser", {}).n("CognitoIdentityProviderClient", "DeleteUserCommand").f(DeleteUserRequestFilterSensitiveLog, void 0).ser(se_DeleteUserCommand).de(de_DeleteUserCommand).build() {
  static {
    __name(this, "DeleteUserCommand");
  }
};

// src/commands/DeleteUserPoolClientCommand.ts



var DeleteUserPoolClientCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DeleteUserPoolClient", {}).n("CognitoIdentityProviderClient", "DeleteUserPoolClientCommand").f(DeleteUserPoolClientRequestFilterSensitiveLog, void 0).ser(se_DeleteUserPoolClientCommand).de(de_DeleteUserPoolClientCommand).build() {
  static {
    __name(this, "DeleteUserPoolClientCommand");
  }
};

// src/commands/DeleteUserPoolCommand.ts



var DeleteUserPoolCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DeleteUserPool", {}).n("CognitoIdentityProviderClient", "DeleteUserPoolCommand").f(void 0, void 0).ser(se_DeleteUserPoolCommand).de(de_DeleteUserPoolCommand).build() {
  static {
    __name(this, "DeleteUserPoolCommand");
  }
};

// src/commands/DeleteUserPoolDomainCommand.ts



var DeleteUserPoolDomainCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DeleteUserPoolDomain", {}).n("CognitoIdentityProviderClient", "DeleteUserPoolDomainCommand").f(void 0, void 0).ser(se_DeleteUserPoolDomainCommand).de(de_DeleteUserPoolDomainCommand).build() {
  static {
    __name(this, "DeleteUserPoolDomainCommand");
  }
};

// src/commands/DeleteWebAuthnCredentialCommand.ts



var DeleteWebAuthnCredentialCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DeleteWebAuthnCredential", {}).n("CognitoIdentityProviderClient", "DeleteWebAuthnCredentialCommand").f(DeleteWebAuthnCredentialRequestFilterSensitiveLog, void 0).ser(se_DeleteWebAuthnCredentialCommand).de(de_DeleteWebAuthnCredentialCommand).build() {
  static {
    __name(this, "DeleteWebAuthnCredentialCommand");
  }
};

// src/commands/DescribeIdentityProviderCommand.ts



var DescribeIdentityProviderCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DescribeIdentityProvider", {}).n("CognitoIdentityProviderClient", "DescribeIdentityProviderCommand").f(void 0, void 0).ser(se_DescribeIdentityProviderCommand).de(de_DescribeIdentityProviderCommand).build() {
  static {
    __name(this, "DescribeIdentityProviderCommand");
  }
};

// src/commands/DescribeManagedLoginBrandingByClientCommand.ts



var DescribeManagedLoginBrandingByClientCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DescribeManagedLoginBrandingByClient", {}).n("CognitoIdentityProviderClient", "DescribeManagedLoginBrandingByClientCommand").f(DescribeManagedLoginBrandingByClientRequestFilterSensitiveLog, void 0).ser(se_DescribeManagedLoginBrandingByClientCommand).de(de_DescribeManagedLoginBrandingByClientCommand).build() {
  static {
    __name(this, "DescribeManagedLoginBrandingByClientCommand");
  }
};

// src/commands/DescribeManagedLoginBrandingCommand.ts



var DescribeManagedLoginBrandingCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DescribeManagedLoginBranding", {}).n("CognitoIdentityProviderClient", "DescribeManagedLoginBrandingCommand").f(void 0, void 0).ser(se_DescribeManagedLoginBrandingCommand).de(de_DescribeManagedLoginBrandingCommand).build() {
  static {
    __name(this, "DescribeManagedLoginBrandingCommand");
  }
};

// src/commands/DescribeResourceServerCommand.ts



var DescribeResourceServerCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DescribeResourceServer", {}).n("CognitoIdentityProviderClient", "DescribeResourceServerCommand").f(void 0, void 0).ser(se_DescribeResourceServerCommand).de(de_DescribeResourceServerCommand).build() {
  static {
    __name(this, "DescribeResourceServerCommand");
  }
};

// src/commands/DescribeRiskConfigurationCommand.ts



var DescribeRiskConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DescribeRiskConfiguration", {}).n("CognitoIdentityProviderClient", "DescribeRiskConfigurationCommand").f(DescribeRiskConfigurationRequestFilterSensitiveLog, DescribeRiskConfigurationResponseFilterSensitiveLog).ser(se_DescribeRiskConfigurationCommand).de(de_DescribeRiskConfigurationCommand).build() {
  static {
    __name(this, "DescribeRiskConfigurationCommand");
  }
};

// src/commands/DescribeUserImportJobCommand.ts



var DescribeUserImportJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DescribeUserImportJob", {}).n("CognitoIdentityProviderClient", "DescribeUserImportJobCommand").f(void 0, void 0).ser(se_DescribeUserImportJobCommand).de(de_DescribeUserImportJobCommand).build() {
  static {
    __name(this, "DescribeUserImportJobCommand");
  }
};

// src/commands/DescribeUserPoolClientCommand.ts



var DescribeUserPoolClientCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DescribeUserPoolClient", {}).n("CognitoIdentityProviderClient", "DescribeUserPoolClientCommand").f(DescribeUserPoolClientRequestFilterSensitiveLog, DescribeUserPoolClientResponseFilterSensitiveLog).ser(se_DescribeUserPoolClientCommand).de(de_DescribeUserPoolClientCommand).build() {
  static {
    __name(this, "DescribeUserPoolClientCommand");
  }
};

// src/commands/DescribeUserPoolCommand.ts



var DescribeUserPoolCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DescribeUserPool", {}).n("CognitoIdentityProviderClient", "DescribeUserPoolCommand").f(void 0, void 0).ser(se_DescribeUserPoolCommand).de(de_DescribeUserPoolCommand).build() {
  static {
    __name(this, "DescribeUserPoolCommand");
  }
};

// src/commands/DescribeUserPoolDomainCommand.ts



var DescribeUserPoolDomainCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "DescribeUserPoolDomain", {}).n("CognitoIdentityProviderClient", "DescribeUserPoolDomainCommand").f(void 0, void 0).ser(se_DescribeUserPoolDomainCommand).de(de_DescribeUserPoolDomainCommand).build() {
  static {
    __name(this, "DescribeUserPoolDomainCommand");
  }
};

// src/commands/ForgetDeviceCommand.ts



var ForgetDeviceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ForgetDevice", {}).n("CognitoIdentityProviderClient", "ForgetDeviceCommand").f(ForgetDeviceRequestFilterSensitiveLog, void 0).ser(se_ForgetDeviceCommand).de(de_ForgetDeviceCommand).build() {
  static {
    __name(this, "ForgetDeviceCommand");
  }
};

// src/commands/ForgotPasswordCommand.ts



var ForgotPasswordCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ForgotPassword", {}).n("CognitoIdentityProviderClient", "ForgotPasswordCommand").f(ForgotPasswordRequestFilterSensitiveLog, void 0).ser(se_ForgotPasswordCommand).de(de_ForgotPasswordCommand).build() {
  static {
    __name(this, "ForgotPasswordCommand");
  }
};

// src/commands/GetCSVHeaderCommand.ts



var GetCSVHeaderCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetCSVHeader", {}).n("CognitoIdentityProviderClient", "GetCSVHeaderCommand").f(void 0, void 0).ser(se_GetCSVHeaderCommand).de(de_GetCSVHeaderCommand).build() {
  static {
    __name(this, "GetCSVHeaderCommand");
  }
};

// src/commands/GetDeviceCommand.ts



var GetDeviceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetDevice", {}).n("CognitoIdentityProviderClient", "GetDeviceCommand").f(GetDeviceRequestFilterSensitiveLog, GetDeviceResponseFilterSensitiveLog).ser(se_GetDeviceCommand).de(de_GetDeviceCommand).build() {
  static {
    __name(this, "GetDeviceCommand");
  }
};

// src/commands/GetGroupCommand.ts



var GetGroupCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetGroup", {}).n("CognitoIdentityProviderClient", "GetGroupCommand").f(void 0, void 0).ser(se_GetGroupCommand).de(de_GetGroupCommand).build() {
  static {
    __name(this, "GetGroupCommand");
  }
};

// src/commands/GetIdentityProviderByIdentifierCommand.ts



var GetIdentityProviderByIdentifierCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetIdentityProviderByIdentifier", {}).n("CognitoIdentityProviderClient", "GetIdentityProviderByIdentifierCommand").f(void 0, void 0).ser(se_GetIdentityProviderByIdentifierCommand).de(de_GetIdentityProviderByIdentifierCommand).build() {
  static {
    __name(this, "GetIdentityProviderByIdentifierCommand");
  }
};

// src/commands/GetLogDeliveryConfigurationCommand.ts



var GetLogDeliveryConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetLogDeliveryConfiguration", {}).n("CognitoIdentityProviderClient", "GetLogDeliveryConfigurationCommand").f(void 0, void 0).ser(se_GetLogDeliveryConfigurationCommand).de(de_GetLogDeliveryConfigurationCommand).build() {
  static {
    __name(this, "GetLogDeliveryConfigurationCommand");
  }
};

// src/commands/GetSigningCertificateCommand.ts



var GetSigningCertificateCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetSigningCertificate", {}).n("CognitoIdentityProviderClient", "GetSigningCertificateCommand").f(void 0, void 0).ser(se_GetSigningCertificateCommand).de(de_GetSigningCertificateCommand).build() {
  static {
    __name(this, "GetSigningCertificateCommand");
  }
};

// src/commands/GetTokensFromRefreshTokenCommand.ts



var GetTokensFromRefreshTokenCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetTokensFromRefreshToken", {}).n("CognitoIdentityProviderClient", "GetTokensFromRefreshTokenCommand").f(GetTokensFromRefreshTokenRequestFilterSensitiveLog, GetTokensFromRefreshTokenResponseFilterSensitiveLog).ser(se_GetTokensFromRefreshTokenCommand).de(de_GetTokensFromRefreshTokenCommand).build() {
  static {
    __name(this, "GetTokensFromRefreshTokenCommand");
  }
};

// src/commands/GetUICustomizationCommand.ts



var GetUICustomizationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetUICustomization", {}).n("CognitoIdentityProviderClient", "GetUICustomizationCommand").f(GetUICustomizationRequestFilterSensitiveLog, GetUICustomizationResponseFilterSensitiveLog).ser(se_GetUICustomizationCommand).de(de_GetUICustomizationCommand).build() {
  static {
    __name(this, "GetUICustomizationCommand");
  }
};

// src/commands/GetUserAttributeVerificationCodeCommand.ts



var GetUserAttributeVerificationCodeCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetUserAttributeVerificationCode", {}).n("CognitoIdentityProviderClient", "GetUserAttributeVerificationCodeCommand").f(GetUserAttributeVerificationCodeRequestFilterSensitiveLog, void 0).ser(se_GetUserAttributeVerificationCodeCommand).de(de_GetUserAttributeVerificationCodeCommand).build() {
  static {
    __name(this, "GetUserAttributeVerificationCodeCommand");
  }
};

// src/commands/GetUserAuthFactorsCommand.ts



var GetUserAuthFactorsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetUserAuthFactors", {}).n("CognitoIdentityProviderClient", "GetUserAuthFactorsCommand").f(GetUserAuthFactorsRequestFilterSensitiveLog, GetUserAuthFactorsResponseFilterSensitiveLog).ser(se_GetUserAuthFactorsCommand).de(de_GetUserAuthFactorsCommand).build() {
  static {
    __name(this, "GetUserAuthFactorsCommand");
  }
};

// src/commands/GetUserCommand.ts



var GetUserCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetUser", {}).n("CognitoIdentityProviderClient", "GetUserCommand").f(GetUserRequestFilterSensitiveLog, GetUserResponseFilterSensitiveLog).ser(se_GetUserCommand).de(de_GetUserCommand).build() {
  static {
    __name(this, "GetUserCommand");
  }
};

// src/commands/GetUserPoolMfaConfigCommand.ts



var GetUserPoolMfaConfigCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GetUserPoolMfaConfig", {}).n("CognitoIdentityProviderClient", "GetUserPoolMfaConfigCommand").f(void 0, void 0).ser(se_GetUserPoolMfaConfigCommand).de(de_GetUserPoolMfaConfigCommand).build() {
  static {
    __name(this, "GetUserPoolMfaConfigCommand");
  }
};

// src/commands/GlobalSignOutCommand.ts



var GlobalSignOutCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "GlobalSignOut", {}).n("CognitoIdentityProviderClient", "GlobalSignOutCommand").f(GlobalSignOutRequestFilterSensitiveLog, void 0).ser(se_GlobalSignOutCommand).de(de_GlobalSignOutCommand).build() {
  static {
    __name(this, "GlobalSignOutCommand");
  }
};

// src/commands/InitiateAuthCommand.ts



var InitiateAuthCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "InitiateAuth", {}).n("CognitoIdentityProviderClient", "InitiateAuthCommand").f(InitiateAuthRequestFilterSensitiveLog, InitiateAuthResponseFilterSensitiveLog).ser(se_InitiateAuthCommand).de(de_InitiateAuthCommand).build() {
  static {
    __name(this, "InitiateAuthCommand");
  }
};

// src/commands/ListDevicesCommand.ts



var ListDevicesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListDevices", {}).n("CognitoIdentityProviderClient", "ListDevicesCommand").f(ListDevicesRequestFilterSensitiveLog, ListDevicesResponseFilterSensitiveLog).ser(se_ListDevicesCommand).de(de_ListDevicesCommand).build() {
  static {
    __name(this, "ListDevicesCommand");
  }
};

// src/commands/ListGroupsCommand.ts



var ListGroupsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListGroups", {}).n("CognitoIdentityProviderClient", "ListGroupsCommand").f(void 0, void 0).ser(se_ListGroupsCommand).de(de_ListGroupsCommand).build() {
  static {
    __name(this, "ListGroupsCommand");
  }
};

// src/commands/ListIdentityProvidersCommand.ts



var ListIdentityProvidersCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListIdentityProviders", {}).n("CognitoIdentityProviderClient", "ListIdentityProvidersCommand").f(void 0, void 0).ser(se_ListIdentityProvidersCommand).de(de_ListIdentityProvidersCommand).build() {
  static {
    __name(this, "ListIdentityProvidersCommand");
  }
};

// src/commands/ListResourceServersCommand.ts



var ListResourceServersCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListResourceServers", {}).n("CognitoIdentityProviderClient", "ListResourceServersCommand").f(void 0, void 0).ser(se_ListResourceServersCommand).de(de_ListResourceServersCommand).build() {
  static {
    __name(this, "ListResourceServersCommand");
  }
};

// src/commands/ListTagsForResourceCommand.ts



var ListTagsForResourceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListTagsForResource", {}).n("CognitoIdentityProviderClient", "ListTagsForResourceCommand").f(void 0, void 0).ser(se_ListTagsForResourceCommand).de(de_ListTagsForResourceCommand).build() {
  static {
    __name(this, "ListTagsForResourceCommand");
  }
};

// src/commands/ListUserImportJobsCommand.ts



var ListUserImportJobsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListUserImportJobs", {}).n("CognitoIdentityProviderClient", "ListUserImportJobsCommand").f(void 0, void 0).ser(se_ListUserImportJobsCommand).de(de_ListUserImportJobsCommand).build() {
  static {
    __name(this, "ListUserImportJobsCommand");
  }
};

// src/commands/ListUserPoolClientsCommand.ts



var ListUserPoolClientsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListUserPoolClients", {}).n("CognitoIdentityProviderClient", "ListUserPoolClientsCommand").f(void 0, ListUserPoolClientsResponseFilterSensitiveLog).ser(se_ListUserPoolClientsCommand).de(de_ListUserPoolClientsCommand).build() {
  static {
    __name(this, "ListUserPoolClientsCommand");
  }
};

// src/commands/ListUserPoolsCommand.ts



var ListUserPoolsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListUserPools", {}).n("CognitoIdentityProviderClient", "ListUserPoolsCommand").f(void 0, void 0).ser(se_ListUserPoolsCommand).de(de_ListUserPoolsCommand).build() {
  static {
    __name(this, "ListUserPoolsCommand");
  }
};

// src/commands/ListUsersCommand.ts



var ListUsersCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListUsers", {}).n("CognitoIdentityProviderClient", "ListUsersCommand").f(void 0, ListUsersResponseFilterSensitiveLog).ser(se_ListUsersCommand).de(de_ListUsersCommand).build() {
  static {
    __name(this, "ListUsersCommand");
  }
};

// src/commands/ListUsersInGroupCommand.ts



var ListUsersInGroupCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListUsersInGroup", {}).n("CognitoIdentityProviderClient", "ListUsersInGroupCommand").f(void 0, ListUsersInGroupResponseFilterSensitiveLog).ser(se_ListUsersInGroupCommand).de(de_ListUsersInGroupCommand).build() {
  static {
    __name(this, "ListUsersInGroupCommand");
  }
};

// src/commands/ListWebAuthnCredentialsCommand.ts



var ListWebAuthnCredentialsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ListWebAuthnCredentials", {}).n("CognitoIdentityProviderClient", "ListWebAuthnCredentialsCommand").f(ListWebAuthnCredentialsRequestFilterSensitiveLog, void 0).ser(se_ListWebAuthnCredentialsCommand).de(de_ListWebAuthnCredentialsCommand).build() {
  static {
    __name(this, "ListWebAuthnCredentialsCommand");
  }
};

// src/commands/ResendConfirmationCodeCommand.ts



var ResendConfirmationCodeCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "ResendConfirmationCode", {}).n("CognitoIdentityProviderClient", "ResendConfirmationCodeCommand").f(ResendConfirmationCodeRequestFilterSensitiveLog, void 0).ser(se_ResendConfirmationCodeCommand).de(de_ResendConfirmationCodeCommand).build() {
  static {
    __name(this, "ResendConfirmationCodeCommand");
  }
};

// src/commands/RespondToAuthChallengeCommand.ts



var RespondToAuthChallengeCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "RespondToAuthChallenge", {}).n("CognitoIdentityProviderClient", "RespondToAuthChallengeCommand").f(RespondToAuthChallengeRequestFilterSensitiveLog, RespondToAuthChallengeResponseFilterSensitiveLog).ser(se_RespondToAuthChallengeCommand).de(de_RespondToAuthChallengeCommand).build() {
  static {
    __name(this, "RespondToAuthChallengeCommand");
  }
};

// src/commands/RevokeTokenCommand.ts



var RevokeTokenCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "RevokeToken", {}).n("CognitoIdentityProviderClient", "RevokeTokenCommand").f(RevokeTokenRequestFilterSensitiveLog, void 0).ser(se_RevokeTokenCommand).de(de_RevokeTokenCommand).build() {
  static {
    __name(this, "RevokeTokenCommand");
  }
};

// src/commands/SetLogDeliveryConfigurationCommand.ts



var SetLogDeliveryConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "SetLogDeliveryConfiguration", {}).n("CognitoIdentityProviderClient", "SetLogDeliveryConfigurationCommand").f(void 0, void 0).ser(se_SetLogDeliveryConfigurationCommand).de(de_SetLogDeliveryConfigurationCommand).build() {
  static {
    __name(this, "SetLogDeliveryConfigurationCommand");
  }
};

// src/commands/SetRiskConfigurationCommand.ts



var SetRiskConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "SetRiskConfiguration", {}).n("CognitoIdentityProviderClient", "SetRiskConfigurationCommand").f(SetRiskConfigurationRequestFilterSensitiveLog, SetRiskConfigurationResponseFilterSensitiveLog).ser(se_SetRiskConfigurationCommand).de(de_SetRiskConfigurationCommand).build() {
  static {
    __name(this, "SetRiskConfigurationCommand");
  }
};

// src/commands/SetUICustomizationCommand.ts



var SetUICustomizationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "SetUICustomization", {}).n("CognitoIdentityProviderClient", "SetUICustomizationCommand").f(SetUICustomizationRequestFilterSensitiveLog, SetUICustomizationResponseFilterSensitiveLog).ser(se_SetUICustomizationCommand).de(de_SetUICustomizationCommand).build() {
  static {
    __name(this, "SetUICustomizationCommand");
  }
};

// src/commands/SetUserMFAPreferenceCommand.ts



var SetUserMFAPreferenceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "SetUserMFAPreference", {}).n("CognitoIdentityProviderClient", "SetUserMFAPreferenceCommand").f(SetUserMFAPreferenceRequestFilterSensitiveLog, void 0).ser(se_SetUserMFAPreferenceCommand).de(de_SetUserMFAPreferenceCommand).build() {
  static {
    __name(this, "SetUserMFAPreferenceCommand");
  }
};

// src/commands/SetUserPoolMfaConfigCommand.ts



var SetUserPoolMfaConfigCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "SetUserPoolMfaConfig", {}).n("CognitoIdentityProviderClient", "SetUserPoolMfaConfigCommand").f(void 0, void 0).ser(se_SetUserPoolMfaConfigCommand).de(de_SetUserPoolMfaConfigCommand).build() {
  static {
    __name(this, "SetUserPoolMfaConfigCommand");
  }
};

// src/commands/SetUserSettingsCommand.ts



var SetUserSettingsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "SetUserSettings", {}).n("CognitoIdentityProviderClient", "SetUserSettingsCommand").f(SetUserSettingsRequestFilterSensitiveLog, void 0).ser(se_SetUserSettingsCommand).de(de_SetUserSettingsCommand).build() {
  static {
    __name(this, "SetUserSettingsCommand");
  }
};

// src/commands/SignUpCommand.ts



var SignUpCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "SignUp", {}).n("CognitoIdentityProviderClient", "SignUpCommand").f(SignUpRequestFilterSensitiveLog, SignUpResponseFilterSensitiveLog).ser(se_SignUpCommand).de(de_SignUpCommand).build() {
  static {
    __name(this, "SignUpCommand");
  }
};

// src/commands/StartUserImportJobCommand.ts



var StartUserImportJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "StartUserImportJob", {}).n("CognitoIdentityProviderClient", "StartUserImportJobCommand").f(void 0, void 0).ser(se_StartUserImportJobCommand).de(de_StartUserImportJobCommand).build() {
  static {
    __name(this, "StartUserImportJobCommand");
  }
};

// src/commands/StartWebAuthnRegistrationCommand.ts



var StartWebAuthnRegistrationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "StartWebAuthnRegistration", {}).n("CognitoIdentityProviderClient", "StartWebAuthnRegistrationCommand").f(StartWebAuthnRegistrationRequestFilterSensitiveLog, void 0).ser(se_StartWebAuthnRegistrationCommand).de(de_StartWebAuthnRegistrationCommand).build() {
  static {
    __name(this, "StartWebAuthnRegistrationCommand");
  }
};

// src/commands/StopUserImportJobCommand.ts



var StopUserImportJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "StopUserImportJob", {}).n("CognitoIdentityProviderClient", "StopUserImportJobCommand").f(void 0, void 0).ser(se_StopUserImportJobCommand).de(de_StopUserImportJobCommand).build() {
  static {
    __name(this, "StopUserImportJobCommand");
  }
};

// src/commands/TagResourceCommand.ts



var TagResourceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "TagResource", {}).n("CognitoIdentityProviderClient", "TagResourceCommand").f(void 0, void 0).ser(se_TagResourceCommand).de(de_TagResourceCommand).build() {
  static {
    __name(this, "TagResourceCommand");
  }
};

// src/commands/UntagResourceCommand.ts



var UntagResourceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UntagResource", {}).n("CognitoIdentityProviderClient", "UntagResourceCommand").f(void 0, void 0).ser(se_UntagResourceCommand).de(de_UntagResourceCommand).build() {
  static {
    __name(this, "UntagResourceCommand");
  }
};

// src/commands/UpdateAuthEventFeedbackCommand.ts



var UpdateAuthEventFeedbackCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UpdateAuthEventFeedback", {}).n("CognitoIdentityProviderClient", "UpdateAuthEventFeedbackCommand").f(UpdateAuthEventFeedbackRequestFilterSensitiveLog, void 0).ser(se_UpdateAuthEventFeedbackCommand).de(de_UpdateAuthEventFeedbackCommand).build() {
  static {
    __name(this, "UpdateAuthEventFeedbackCommand");
  }
};

// src/commands/UpdateDeviceStatusCommand.ts



var UpdateDeviceStatusCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UpdateDeviceStatus", {}).n("CognitoIdentityProviderClient", "UpdateDeviceStatusCommand").f(UpdateDeviceStatusRequestFilterSensitiveLog, void 0).ser(se_UpdateDeviceStatusCommand).de(de_UpdateDeviceStatusCommand).build() {
  static {
    __name(this, "UpdateDeviceStatusCommand");
  }
};

// src/commands/UpdateGroupCommand.ts



var UpdateGroupCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UpdateGroup", {}).n("CognitoIdentityProviderClient", "UpdateGroupCommand").f(void 0, void 0).ser(se_UpdateGroupCommand).de(de_UpdateGroupCommand).build() {
  static {
    __name(this, "UpdateGroupCommand");
  }
};

// src/commands/UpdateIdentityProviderCommand.ts



var UpdateIdentityProviderCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UpdateIdentityProvider", {}).n("CognitoIdentityProviderClient", "UpdateIdentityProviderCommand").f(void 0, void 0).ser(se_UpdateIdentityProviderCommand).de(de_UpdateIdentityProviderCommand).build() {
  static {
    __name(this, "UpdateIdentityProviderCommand");
  }
};

// src/commands/UpdateManagedLoginBrandingCommand.ts



var UpdateManagedLoginBrandingCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UpdateManagedLoginBranding", {}).n("CognitoIdentityProviderClient", "UpdateManagedLoginBrandingCommand").f(void 0, void 0).ser(se_UpdateManagedLoginBrandingCommand).de(de_UpdateManagedLoginBrandingCommand).build() {
  static {
    __name(this, "UpdateManagedLoginBrandingCommand");
  }
};

// src/commands/UpdateResourceServerCommand.ts



var UpdateResourceServerCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UpdateResourceServer", {}).n("CognitoIdentityProviderClient", "UpdateResourceServerCommand").f(void 0, void 0).ser(se_UpdateResourceServerCommand).de(de_UpdateResourceServerCommand).build() {
  static {
    __name(this, "UpdateResourceServerCommand");
  }
};

// src/commands/UpdateUserAttributesCommand.ts



var UpdateUserAttributesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UpdateUserAttributes", {}).n("CognitoIdentityProviderClient", "UpdateUserAttributesCommand").f(UpdateUserAttributesRequestFilterSensitiveLog, void 0).ser(se_UpdateUserAttributesCommand).de(de_UpdateUserAttributesCommand).build() {
  static {
    __name(this, "UpdateUserAttributesCommand");
  }
};

// src/commands/UpdateUserPoolClientCommand.ts



var UpdateUserPoolClientCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UpdateUserPoolClient", {}).n("CognitoIdentityProviderClient", "UpdateUserPoolClientCommand").f(UpdateUserPoolClientRequestFilterSensitiveLog, UpdateUserPoolClientResponseFilterSensitiveLog).ser(se_UpdateUserPoolClientCommand).de(de_UpdateUserPoolClientCommand).build() {
  static {
    __name(this, "UpdateUserPoolClientCommand");
  }
};

// src/commands/UpdateUserPoolCommand.ts



var UpdateUserPoolCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UpdateUserPool", {}).n("CognitoIdentityProviderClient", "UpdateUserPoolCommand").f(void 0, void 0).ser(se_UpdateUserPoolCommand).de(de_UpdateUserPoolCommand).build() {
  static {
    __name(this, "UpdateUserPoolCommand");
  }
};

// src/commands/UpdateUserPoolDomainCommand.ts



var UpdateUserPoolDomainCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "UpdateUserPoolDomain", {}).n("CognitoIdentityProviderClient", "UpdateUserPoolDomainCommand").f(void 0, void 0).ser(se_UpdateUserPoolDomainCommand).de(de_UpdateUserPoolDomainCommand).build() {
  static {
    __name(this, "UpdateUserPoolDomainCommand");
  }
};

// src/commands/VerifySoftwareTokenCommand.ts



var VerifySoftwareTokenCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "VerifySoftwareToken", {}).n("CognitoIdentityProviderClient", "VerifySoftwareTokenCommand").f(VerifySoftwareTokenRequestFilterSensitiveLog, VerifySoftwareTokenResponseFilterSensitiveLog).ser(se_VerifySoftwareTokenCommand).de(de_VerifySoftwareTokenCommand).build() {
  static {
    __name(this, "VerifySoftwareTokenCommand");
  }
};

// src/commands/VerifyUserAttributeCommand.ts



var VerifyUserAttributeCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AWSCognitoIdentityProviderService", "VerifyUserAttribute", {}).n("CognitoIdentityProviderClient", "VerifyUserAttributeCommand").f(VerifyUserAttributeRequestFilterSensitiveLog, void 0).ser(se_VerifyUserAttributeCommand).de(de_VerifyUserAttributeCommand).build() {
  static {
    __name(this, "VerifyUserAttributeCommand");
  }
};

// src/CognitoIdentityProvider.ts
var commands = {
  AddCustomAttributesCommand,
  AdminAddUserToGroupCommand,
  AdminConfirmSignUpCommand,
  AdminCreateUserCommand,
  AdminDeleteUserCommand,
  AdminDeleteUserAttributesCommand,
  AdminDisableProviderForUserCommand,
  AdminDisableUserCommand,
  AdminEnableUserCommand,
  AdminForgetDeviceCommand,
  AdminGetDeviceCommand,
  AdminGetUserCommand,
  AdminInitiateAuthCommand,
  AdminLinkProviderForUserCommand,
  AdminListDevicesCommand,
  AdminListGroupsForUserCommand,
  AdminListUserAuthEventsCommand,
  AdminRemoveUserFromGroupCommand,
  AdminResetUserPasswordCommand,
  AdminRespondToAuthChallengeCommand,
  AdminSetUserMFAPreferenceCommand,
  AdminSetUserPasswordCommand,
  AdminSetUserSettingsCommand,
  AdminUpdateAuthEventFeedbackCommand,
  AdminUpdateDeviceStatusCommand,
  AdminUpdateUserAttributesCommand,
  AdminUserGlobalSignOutCommand,
  AssociateSoftwareTokenCommand,
  ChangePasswordCommand,
  CompleteWebAuthnRegistrationCommand,
  ConfirmDeviceCommand,
  ConfirmForgotPasswordCommand,
  ConfirmSignUpCommand,
  CreateGroupCommand,
  CreateIdentityProviderCommand,
  CreateManagedLoginBrandingCommand,
  CreateResourceServerCommand,
  CreateUserImportJobCommand,
  CreateUserPoolCommand,
  CreateUserPoolClientCommand,
  CreateUserPoolDomainCommand,
  DeleteGroupCommand,
  DeleteIdentityProviderCommand,
  DeleteManagedLoginBrandingCommand,
  DeleteResourceServerCommand,
  DeleteUserCommand,
  DeleteUserAttributesCommand,
  DeleteUserPoolCommand,
  DeleteUserPoolClientCommand,
  DeleteUserPoolDomainCommand,
  DeleteWebAuthnCredentialCommand,
  DescribeIdentityProviderCommand,
  DescribeManagedLoginBrandingCommand,
  DescribeManagedLoginBrandingByClientCommand,
  DescribeResourceServerCommand,
  DescribeRiskConfigurationCommand,
  DescribeUserImportJobCommand,
  DescribeUserPoolCommand,
  DescribeUserPoolClientCommand,
  DescribeUserPoolDomainCommand,
  ForgetDeviceCommand,
  ForgotPasswordCommand,
  GetCSVHeaderCommand,
  GetDeviceCommand,
  GetGroupCommand,
  GetIdentityProviderByIdentifierCommand,
  GetLogDeliveryConfigurationCommand,
  GetSigningCertificateCommand,
  GetTokensFromRefreshTokenCommand,
  GetUICustomizationCommand,
  GetUserCommand,
  GetUserAttributeVerificationCodeCommand,
  GetUserAuthFactorsCommand,
  GetUserPoolMfaConfigCommand,
  GlobalSignOutCommand,
  InitiateAuthCommand,
  ListDevicesCommand,
  ListGroupsCommand,
  ListIdentityProvidersCommand,
  ListResourceServersCommand,
  ListTagsForResourceCommand,
  ListUserImportJobsCommand,
  ListUserPoolClientsCommand,
  ListUserPoolsCommand,
  ListUsersCommand,
  ListUsersInGroupCommand,
  ListWebAuthnCredentialsCommand,
  ResendConfirmationCodeCommand,
  RespondToAuthChallengeCommand,
  RevokeTokenCommand,
  SetLogDeliveryConfigurationCommand,
  SetRiskConfigurationCommand,
  SetUICustomizationCommand,
  SetUserMFAPreferenceCommand,
  SetUserPoolMfaConfigCommand,
  SetUserSettingsCommand,
  SignUpCommand,
  StartUserImportJobCommand,
  StartWebAuthnRegistrationCommand,
  StopUserImportJobCommand,
  TagResourceCommand,
  UntagResourceCommand,
  UpdateAuthEventFeedbackCommand,
  UpdateDeviceStatusCommand,
  UpdateGroupCommand,
  UpdateIdentityProviderCommand,
  UpdateManagedLoginBrandingCommand,
  UpdateResourceServerCommand,
  UpdateUserAttributesCommand,
  UpdateUserPoolCommand,
  UpdateUserPoolClientCommand,
  UpdateUserPoolDomainCommand,
  VerifySoftwareTokenCommand,
  VerifyUserAttributeCommand
};
var CognitoIdentityProvider = class extends CognitoIdentityProviderClient {
  static {
    __name(this, "CognitoIdentityProvider");
  }
};
(0, import_smithy_client.createAggregatedClient)(commands, CognitoIdentityProvider);

// src/pagination/AdminListGroupsForUserPaginator.ts

var paginateAdminListGroupsForUser = (0, import_core.createPaginator)(CognitoIdentityProviderClient, AdminListGroupsForUserCommand, "NextToken", "NextToken", "Limit");

// src/pagination/AdminListUserAuthEventsPaginator.ts

var paginateAdminListUserAuthEvents = (0, import_core.createPaginator)(CognitoIdentityProviderClient, AdminListUserAuthEventsCommand, "NextToken", "NextToken", "MaxResults");

// src/pagination/ListGroupsPaginator.ts

var paginateListGroups = (0, import_core.createPaginator)(CognitoIdentityProviderClient, ListGroupsCommand, "NextToken", "NextToken", "Limit");

// src/pagination/ListIdentityProvidersPaginator.ts

var paginateListIdentityProviders = (0, import_core.createPaginator)(CognitoIdentityProviderClient, ListIdentityProvidersCommand, "NextToken", "NextToken", "MaxResults");

// src/pagination/ListResourceServersPaginator.ts

var paginateListResourceServers = (0, import_core.createPaginator)(CognitoIdentityProviderClient, ListResourceServersCommand, "NextToken", "NextToken", "MaxResults");

// src/pagination/ListUserPoolClientsPaginator.ts

var paginateListUserPoolClients = (0, import_core.createPaginator)(CognitoIdentityProviderClient, ListUserPoolClientsCommand, "NextToken", "NextToken", "MaxResults");

// src/pagination/ListUserPoolsPaginator.ts

var paginateListUserPools = (0, import_core.createPaginator)(CognitoIdentityProviderClient, ListUserPoolsCommand, "NextToken", "NextToken", "MaxResults");

// src/pagination/ListUsersInGroupPaginator.ts

var paginateListUsersInGroup = (0, import_core.createPaginator)(CognitoIdentityProviderClient, ListUsersInGroupCommand, "NextToken", "NextToken", "Limit");

// src/pagination/ListUsersPaginator.ts

var paginateListUsers = (0, import_core.createPaginator)(CognitoIdentityProviderClient, ListUsersCommand, "PaginationToken", "PaginationToken", "Limit");
// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  CognitoIdentityProviderServiceException,
  __Client,
  CognitoIdentityProviderClient,
  CognitoIdentityProvider,
  $Command,
  AddCustomAttributesCommand,
  AdminAddUserToGroupCommand,
  AdminConfirmSignUpCommand,
  AdminCreateUserCommand,
  AdminDeleteUserAttributesCommand,
  AdminDeleteUserCommand,
  AdminDisableProviderForUserCommand,
  AdminDisableUserCommand,
  AdminEnableUserCommand,
  AdminForgetDeviceCommand,
  AdminGetDeviceCommand,
  AdminGetUserCommand,
  AdminInitiateAuthCommand,
  AdminLinkProviderForUserCommand,
  AdminListDevicesCommand,
  AdminListGroupsForUserCommand,
  AdminListUserAuthEventsCommand,
  AdminRemoveUserFromGroupCommand,
  AdminResetUserPasswordCommand,
  AdminRespondToAuthChallengeCommand,
  AdminSetUserMFAPreferenceCommand,
  AdminSetUserPasswordCommand,
  AdminSetUserSettingsCommand,
  AdminUpdateAuthEventFeedbackCommand,
  AdminUpdateDeviceStatusCommand,
  AdminUpdateUserAttributesCommand,
  AdminUserGlobalSignOutCommand,
  AssociateSoftwareTokenCommand,
  ChangePasswordCommand,
  CompleteWebAuthnRegistrationCommand,
  ConfirmDeviceCommand,
  ConfirmForgotPasswordCommand,
  ConfirmSignUpCommand,
  CreateGroupCommand,
  CreateIdentityProviderCommand,
  CreateManagedLoginBrandingCommand,
  CreateResourceServerCommand,
  CreateUserImportJobCommand,
  CreateUserPoolClientCommand,
  CreateUserPoolCommand,
  CreateUserPoolDomainCommand,
  DeleteGroupCommand,
  DeleteIdentityProviderCommand,
  DeleteManagedLoginBrandingCommand,
  DeleteResourceServerCommand,
  DeleteUserAttributesCommand,
  DeleteUserCommand,
  DeleteUserPoolClientCommand,
  DeleteUserPoolCommand,
  DeleteUserPoolDomainCommand,
  DeleteWebAuthnCredentialCommand,
  DescribeIdentityProviderCommand,
  DescribeManagedLoginBrandingByClientCommand,
  DescribeManagedLoginBrandingCommand,
  DescribeResourceServerCommand,
  DescribeRiskConfigurationCommand,
  DescribeUserImportJobCommand,
  DescribeUserPoolClientCommand,
  DescribeUserPoolCommand,
  DescribeUserPoolDomainCommand,
  ForgetDeviceCommand,
  ForgotPasswordCommand,
  GetCSVHeaderCommand,
  GetDeviceCommand,
  GetGroupCommand,
  GetIdentityProviderByIdentifierCommand,
  GetLogDeliveryConfigurationCommand,
  GetSigningCertificateCommand,
  GetTokensFromRefreshTokenCommand,
  GetUICustomizationCommand,
  GetUserAttributeVerificationCodeCommand,
  GetUserAuthFactorsCommand,
  GetUserCommand,
  GetUserPoolMfaConfigCommand,
  GlobalSignOutCommand,
  InitiateAuthCommand,
  ListDevicesCommand,
  ListGroupsCommand,
  ListIdentityProvidersCommand,
  ListResourceServersCommand,
  ListTagsForResourceCommand,
  ListUserImportJobsCommand,
  ListUserPoolClientsCommand,
  ListUserPoolsCommand,
  ListUsersCommand,
  ListUsersInGroupCommand,
  ListWebAuthnCredentialsCommand,
  ResendConfirmationCodeCommand,
  RespondToAuthChallengeCommand,
  RevokeTokenCommand,
  SetLogDeliveryConfigurationCommand,
  SetRiskConfigurationCommand,
  SetUICustomizationCommand,
  SetUserMFAPreferenceCommand,
  SetUserPoolMfaConfigCommand,
  SetUserSettingsCommand,
  SignUpCommand,
  StartUserImportJobCommand,
  StartWebAuthnRegistrationCommand,
  StopUserImportJobCommand,
  TagResourceCommand,
  UntagResourceCommand,
  UpdateAuthEventFeedbackCommand,
  UpdateDeviceStatusCommand,
  UpdateGroupCommand,
  UpdateIdentityProviderCommand,
  UpdateManagedLoginBrandingCommand,
  UpdateResourceServerCommand,
  UpdateUserAttributesCommand,
  UpdateUserPoolClientCommand,
  UpdateUserPoolCommand,
  UpdateUserPoolDomainCommand,
  VerifySoftwareTokenCommand,
  VerifyUserAttributeCommand,
  paginateAdminListGroupsForUser,
  paginateAdminListUserAuthEvents,
  paginateListGroups,
  paginateListIdentityProviders,
  paginateListResourceServers,
  paginateListUserPoolClients,
  paginateListUserPools,
  paginateListUsersInGroup,
  paginateListUsers,
  RecoveryOptionNameType,
  AccountTakeoverEventActionType,
  AttributeDataType,
  InternalErrorException,
  InvalidParameterException,
  NotAuthorizedException,
  ResourceNotFoundException,
  TooManyRequestsException,
  UserImportInProgressException,
  UserNotFoundException,
  InvalidLambdaResponseException,
  LimitExceededException,
  TooManyFailedAttemptsException,
  UnexpectedLambdaException,
  UserLambdaValidationException,
  DeliveryMediumType,
  MessageActionType,
  UserStatusType,
  CodeDeliveryFailureException,
  InvalidPasswordException,
  InvalidSmsRoleAccessPolicyException,
  InvalidSmsRoleTrustRelationshipException,
  PreconditionNotMetException,
  UnsupportedUserStateException,
  UsernameExistsException,
  AliasExistsException,
  InvalidUserPoolConfigurationException,
  AuthFlowType,
  ChallengeNameType,
  InvalidEmailRoleAccessPolicyException,
  MFAMethodNotFoundException,
  PasswordResetRequiredException,
  UnsupportedOperationException,
  UserNotConfirmedException,
  ChallengeName,
  ChallengeResponse,
  FeedbackValueType,
  EventResponseType,
  RiskDecisionType,
  RiskLevelType,
  EventType,
  UserPoolAddOnNotEnabledException,
  CodeMismatchException,
  ExpiredCodeException,
  PasswordHistoryPolicyViolationException,
  SoftwareTokenMFANotFoundException,
  DeviceRememberedStatusType,
  AdvancedSecurityEnabledModeType,
  AdvancedSecurityModeType,
  AliasAttributeType,
  AuthFactorType,
  AssetCategoryType,
  AssetExtensionType,
  ColorSchemeModeType,
  ConcurrentModificationException,
  ForbiddenException,
  VerifiedAttributeType,
  WebAuthnChallengeNotFoundException,
  WebAuthnClientMismatchException,
  WebAuthnCredentialNotSupportedException,
  WebAuthnNotEnabledException,
  WebAuthnOriginNotAllowedException,
  WebAuthnRelyingPartyMismatchException,
  DeviceKeyExistsException,
  GroupExistsException,
  IdentityProviderTypeType,
  DuplicateProviderException,
  ManagedLoginBrandingExistsException,
  UserImportJobStatusType,
  DeletionProtectionType,
  EmailSendingAccountType,
  CustomEmailSenderLambdaVersionType,
  CustomSMSSenderLambdaVersionType,
  PreTokenGenerationLambdaVersionType,
  UserPoolMfaType,
  UsernameAttributeType,
  UserPoolTierType,
  DefaultEmailOptionType,
  StatusType,
  FeatureUnavailableInTierException,
  TierChangeNotAllowedException,
  UserPoolTaggingException,
  OAuthFlowType,
  ExplicitAuthFlowsType,
  PreventUserExistenceErrorTypes,
  FeatureType,
  TimeUnitsType,
  InvalidOAuthFlowException,
  ScopeDoesNotExistException,
  UnsupportedIdentityProviderException,
  CompromisedCredentialsEventActionType,
  EventFilterType,
  DomainStatusType,
  EventSourceName,
  LogLevel,
  RefreshTokenReuseException,
  AdminAddUserToGroupRequestFilterSensitiveLog,
  AdminConfirmSignUpRequestFilterSensitiveLog,
  AttributeTypeFilterSensitiveLog,
  AdminCreateUserRequestFilterSensitiveLog,
  UserTypeFilterSensitiveLog,
  AdminCreateUserResponseFilterSensitiveLog,
  AdminDeleteUserRequestFilterSensitiveLog,
  AdminDeleteUserAttributesRequestFilterSensitiveLog,
  AdminDisableUserRequestFilterSensitiveLog,
  AdminEnableUserRequestFilterSensitiveLog,
  AdminForgetDeviceRequestFilterSensitiveLog,
  AdminGetDeviceRequestFilterSensitiveLog,
  DeviceTypeFilterSensitiveLog,
  AdminGetDeviceResponseFilterSensitiveLog,
  AdminGetUserRequestFilterSensitiveLog,
  AdminGetUserResponseFilterSensitiveLog,
  AdminInitiateAuthRequestFilterSensitiveLog,
  AuthenticationResultTypeFilterSensitiveLog,
  AdminInitiateAuthResponseFilterSensitiveLog,
  AdminListDevicesRequestFilterSensitiveLog,
  AdminListDevicesResponseFilterSensitiveLog,
  AdminListGroupsForUserRequestFilterSensitiveLog,
  AdminListUserAuthEventsRequestFilterSensitiveLog,
  AdminRemoveUserFromGroupRequestFilterSensitiveLog,
  AdminResetUserPasswordRequestFilterSensitiveLog,
  AdminRespondToAuthChallengeRequestFilterSensitiveLog,
  AdminRespondToAuthChallengeResponseFilterSensitiveLog,
  AdminSetUserMFAPreferenceRequestFilterSensitiveLog,
  AdminSetUserPasswordRequestFilterSensitiveLog,
  AdminSetUserSettingsRequestFilterSensitiveLog,
  AdminUpdateAuthEventFeedbackRequestFilterSensitiveLog,
  AdminUpdateDeviceStatusRequestFilterSensitiveLog,
  AdminUpdateUserAttributesRequestFilterSensitiveLog,
  AdminUserGlobalSignOutRequestFilterSensitiveLog,
  AssociateSoftwareTokenRequestFilterSensitiveLog,
  AssociateSoftwareTokenResponseFilterSensitiveLog,
  ChangePasswordRequestFilterSensitiveLog,
  CompleteWebAuthnRegistrationRequestFilterSensitiveLog,
  ConfirmDeviceRequestFilterSensitiveLog,
  UserContextDataTypeFilterSensitiveLog,
  ConfirmForgotPasswordRequestFilterSensitiveLog,
  ConfirmSignUpRequestFilterSensitiveLog,
  ConfirmSignUpResponseFilterSensitiveLog,
  CreateManagedLoginBrandingRequestFilterSensitiveLog,
  UserPoolClientTypeFilterSensitiveLog,
  CreateUserPoolClientResponseFilterSensitiveLog,
  DeleteUserRequestFilterSensitiveLog,
  DeleteUserAttributesRequestFilterSensitiveLog,
  DeleteUserPoolClientRequestFilterSensitiveLog,
  DeleteWebAuthnCredentialRequestFilterSensitiveLog,
  DescribeManagedLoginBrandingByClientRequestFilterSensitiveLog,
  DescribeRiskConfigurationRequestFilterSensitiveLog,
  RiskConfigurationTypeFilterSensitiveLog,
  DescribeRiskConfigurationResponseFilterSensitiveLog,
  DescribeUserPoolClientRequestFilterSensitiveLog,
  DescribeUserPoolClientResponseFilterSensitiveLog,
  ForgetDeviceRequestFilterSensitiveLog,
  ForgotPasswordRequestFilterSensitiveLog,
  GetDeviceRequestFilterSensitiveLog,
  GetDeviceResponseFilterSensitiveLog,
  GetTokensFromRefreshTokenRequestFilterSensitiveLog,
  GetTokensFromRefreshTokenResponseFilterSensitiveLog,
  GetUICustomizationRequestFilterSensitiveLog,
  UICustomizationTypeFilterSensitiveLog,
  GetUICustomizationResponseFilterSensitiveLog,
  GetUserRequestFilterSensitiveLog,
  GetUserResponseFilterSensitiveLog,
  GetUserAttributeVerificationCodeRequestFilterSensitiveLog,
  UserVerificationType,
  UnauthorizedException,
  UnsupportedTokenTypeException,
  WebAuthnConfigurationMissingException,
  EnableSoftwareTokenMFAException,
  VerifySoftwareTokenResponseType,
  GetUserAuthFactorsRequestFilterSensitiveLog,
  GetUserAuthFactorsResponseFilterSensitiveLog,
  GlobalSignOutRequestFilterSensitiveLog,
  InitiateAuthRequestFilterSensitiveLog,
  InitiateAuthResponseFilterSensitiveLog,
  ListDevicesRequestFilterSensitiveLog,
  ListDevicesResponseFilterSensitiveLog,
  UserPoolClientDescriptionFilterSensitiveLog,
  ListUserPoolClientsResponseFilterSensitiveLog,
  ListUsersResponseFilterSensitiveLog,
  ListUsersInGroupResponseFilterSensitiveLog,
  ListWebAuthnCredentialsRequestFilterSensitiveLog,
  ResendConfirmationCodeRequestFilterSensitiveLog,
  RespondToAuthChallengeRequestFilterSensitiveLog,
  RespondToAuthChallengeResponseFilterSensitiveLog,
  RevokeTokenRequestFilterSensitiveLog,
  SetRiskConfigurationRequestFilterSensitiveLog,
  SetRiskConfigurationResponseFilterSensitiveLog,
  SetUICustomizationRequestFilterSensitiveLog,
  SetUICustomizationResponseFilterSensitiveLog,
  SetUserMFAPreferenceRequestFilterSensitiveLog,
  SetUserSettingsRequestFilterSensitiveLog,
  SignUpRequestFilterSensitiveLog,
  SignUpResponseFilterSensitiveLog,
  StartWebAuthnRegistrationRequestFilterSensitiveLog,
  UpdateAuthEventFeedbackRequestFilterSensitiveLog,
  UpdateDeviceStatusRequestFilterSensitiveLog,
  UpdateUserAttributesRequestFilterSensitiveLog,
  UpdateUserPoolClientRequestFilterSensitiveLog,
  UpdateUserPoolClientResponseFilterSensitiveLog,
  VerifySoftwareTokenRequestFilterSensitiveLog,
  VerifySoftwareTokenResponseFilterSensitiveLog,
  VerifyUserAttributeRequestFilterSensitiveLog
});

