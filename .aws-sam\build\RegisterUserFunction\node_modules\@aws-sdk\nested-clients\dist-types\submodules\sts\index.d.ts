/**
 * <fullname>Security Token Service</fullname>
 *          <p>Security Token Service (STS) enables you to request temporary, limited-privilege
 *       credentials for users. This guide provides descriptions of the STS API. For
 *       more information about using this service, see <a href="https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_temp.html">Temporary Security Credentials</a>.</p>
 *
 * @packageDocumentation
 */
export * from "./STSClient";
export * from "./STS";
export { ClientInputEndpointParameters } from "./endpoint/EndpointParameters";
export type { RuntimeExtension } from "./runtimeExtensions";
export type { STSExtensionConfiguration } from "./extensionConfiguration";
export * from "./commands";
export * from "./models";
export * from "./defaultRoleAssumers";
export { STSServiceException } from "./models/STSServiceException";
