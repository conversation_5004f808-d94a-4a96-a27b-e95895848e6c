import {
  HttpRequest as __HttpRequest,
  HttpResponse as __HttpResponse,
} from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import {
  AssumeRoleCommandInput,
  AssumeRoleCommandOutput,
} from "../commands/AssumeRoleCommand";
import {
  AssumeRoleWithWebIdentityCommandInput,
  AssumeRoleWithWebIdentityCommandOutput,
} from "../commands/AssumeRoleWithWebIdentityCommand";
export declare const se_AssumeRoleCommand: (
  input: AssumeRoleCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_AssumeRoleWithWebIdentityCommand: (
  input: AssumeRoleWithWebIdentityCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const de_AssumeRoleCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<AssumeRoleCommandOutput>;
export declare const de_AssumeRoleWithWebIdentityCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<AssumeRoleWithWebIdentityCommandOutput>;
