# This file is auto generated by SAM CLI build command

[function_build_definitions.26d60a2f-ef78-4716-b39b-be8e2d88f45e]
codeuri = "D:\\Vinod\\Work\\AugmentCode_Projects\\Tunami_MVP\\backend\\src"
runtime = "nodejs18.x"
architecture = "x86_64"
handler = "handlers/registerUser.handler"
manifest_hash = ""
packagetype = "Zip"
functions = ["RegisterUserFunction", "LoginUserFunction", "GetCurrentUserFunction"]

[layer_build_definitions]
