import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { AdminRemoveUserFromGroupRequestFilterSensitiveLog } from "../models/models_0";
import { de_AdminRemoveUserFromGroupCommand, se_AdminRemoveUserFromGroupCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class AdminRemoveUserFromGroupCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSCognitoIdentityProviderService", "AdminRemoveUserFromGroup", {})
    .n("CognitoIdentityProviderClient", "AdminRemoveUserFromGroupCommand")
    .f(AdminRemoveUserFromGroupRequestFilterSensitiveLog, void 0)
    .ser(se_AdminRemoveUserFromGroupCommand)
    .de(de_AdminRemoveUserFromGroupCommand)
    .build() {
}
