import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import {
  ListContributorInsightsInput,
  ListContributorInsightsOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ListContributorInsightsCommandInput
  extends ListContributorInsightsInput {}
export interface ListContributorInsightsCommandOutput
  extends ListContributorInsightsOutput,
    __MetadataBearer {}
declare const ListContributorInsightsCommand_base: {
  new (
    input: ListContributorInsightsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListContributorInsightsCommandInput,
    ListContributorInsightsCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListContributorInsightsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListContributorInsightsCommandInput,
    ListContributorInsightsCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListContributorInsightsCommand extends ListContributorInsightsCommand_base {
  protected static __types: {
    api: {
      input: ListContributorInsightsInput;
      output: ListContributorInsightsOutput;
    };
    sdk: {
      input: ListContributorInsightsCommandInput;
      output: ListContributorInsightsCommandOutput;
    };
  };
}
