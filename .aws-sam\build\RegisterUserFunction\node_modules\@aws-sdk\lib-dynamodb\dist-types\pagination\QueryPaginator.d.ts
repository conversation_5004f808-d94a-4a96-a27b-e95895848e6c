import { Paginator } from "@smithy/types";
import { QueryCommandInput, QueryCommandOutput } from "../commands/QueryCommand";
import { DynamoDBDocumentPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export { Paginator };
/**
 * @public
 */
export declare const paginateQuery: (config: DynamoDBDocumentPaginationConfiguration, input: QueryCommandInput, ...additionalArguments: any) => Paginator<QueryCommandOutput>;
