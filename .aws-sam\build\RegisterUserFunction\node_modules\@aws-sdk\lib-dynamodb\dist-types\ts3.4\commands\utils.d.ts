import { marshallOptions, unmarshallOptions } from "@aws-sdk/util-dynamodb";
export type KeyNodeSelf = null;
export declare const SELF: KeyNodeSelf;
export type KeyNodeChildren = Record<string, any>;
export declare const ALL_VALUES: KeyNodeChildren;
export declare const ALL_MEMBERS: KeyNodeChildren;
export type KeyNodes = KeyNodeSelf | KeyNodeChildren;
export declare const marshallInput: (
  obj: any,
  keyNodes: KeyNodeChildren,
  options?: marshallOptions
) => any;
export declare const unmarshallOutput: (
  obj: any,
  keyNodes: KeyNodeChildren,
  options?: unmarshallOptions
) => any;
