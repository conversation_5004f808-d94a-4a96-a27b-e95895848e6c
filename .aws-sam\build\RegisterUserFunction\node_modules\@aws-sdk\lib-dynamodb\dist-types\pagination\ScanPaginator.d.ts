import { Paginator } from "@smithy/types";
import { ScanCommandInput, ScanCommandOutput } from "../commands/ScanCommand";
import { DynamoDBDocumentPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export { Paginator };
/**
 * @public
 */
export declare const paginateScan: (config: DynamoDBDocumentPaginationConfiguration, input: ScanCommandInput, ...additionalArguments: any) => Paginator<ScanCommandOutput>;
