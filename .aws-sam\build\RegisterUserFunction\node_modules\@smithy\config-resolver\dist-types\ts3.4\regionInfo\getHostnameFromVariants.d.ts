import { EndpointVariant } from "./EndpointVariant";
/**
 * @internal
 * @deprecated unused as of endpointsRuleSets.
 */
export interface GetHostnameFromVariantsOptions {
    useFipsEndpoint: boolean;
    useDualstackEndpoint: boolean;
}
/**
 * @internal
 * @deprecated unused as of endpointsRuleSets.
 */
export declare const getHostnameFromVariants: (variants: EndpointVariant[] | undefined, { useFipsEndpoint, useDualstackEndpoint }: GetHostnameFromVariantsOptions) => string | undefined;
