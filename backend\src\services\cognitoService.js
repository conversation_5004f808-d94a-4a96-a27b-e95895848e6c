const {
  CognitoIdentityProviderClient,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
  AdminConfirmSignUpCommand,
  AdminInitiateAuthCommand,
  AdminGetUserCommand
} = require('@aws-sdk/client-cognito-identity-provider')

class CognitoService {
  constructor() {
    this.client = new CognitoIdentityProviderClient({
      region: process.env.AWS_REGION || 'us-east-1'
    })
    this.userPoolId = process.env.USER_POOL_ID
    this.clientId = process.env.USER_POOL_CLIENT_ID
  }

  /**
   * Creates a new user in Cognito User Pool
   * @param {string} username - Username
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} - Created user information
   */
  async createUser(username, email, password) {
    try {
      // Create user
      const createUserCommand = new AdminCreateUserCommand({
        UserPoolId: this.userPoolId,
        Username: email, // Using email as username
        UserAttributes: [
          {
            Name: 'email',
            Value: email
          },
          {
            Name: 'username',
            Value: username
          },
          {
            Name: 'email_verified',
            Value: 'true'
          }
        ],
        MessageAction: 'SUPPRESS', // Don't send welcome email
        TemporaryPassword: password
      })

      const createResult = await this.client.send(createUserCommand)

      // Set permanent password
      const setPasswordCommand = new AdminSetUserPasswordCommand({
        UserPoolId: this.userPoolId,
        Username: email,
        Password: password,
        Permanent: true
      })

      await this.client.send(setPasswordCommand)

      // Confirm user signup
      const confirmCommand = new AdminConfirmSignUpCommand({
        UserPoolId: this.userPoolId,
        Username: email
      })

      await this.client.send(confirmCommand)

      return {
        userId: createResult.User.Username,
        email: email,
        username: username
      }
    } catch (error) {
      console.error('Error creating user in Cognito:', error)
      
      if (error.name === 'UsernameExistsException') {
        throw new Error('User with this email already exists')
      }
      
      throw new Error(`Failed to create user: ${error.message}`)
    }
  }

  /**
   * Authenticates a user and returns tokens
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} - Authentication tokens and user info
   */
  async authenticateUser(email, password) {
    try {
      const authCommand = new AdminInitiateAuthCommand({
        UserPoolId: this.userPoolId,
        ClientId: this.clientId,
        AuthFlow: 'ADMIN_NO_SRP_AUTH',
        AuthParameters: {
          USERNAME: email,
          PASSWORD: password
        }
      })

      const authResult = await this.client.send(authCommand)

      if (!authResult.AuthenticationResult) {
        throw new Error('Authentication failed')
      }

      // Get user details
      const getUserCommand = new AdminGetUserCommand({
        UserPoolId: this.userPoolId,
        Username: email
      })

      const userResult = await this.client.send(getUserCommand)
      
      const userAttributes = {}
      userResult.UserAttributes.forEach(attr => {
        userAttributes[attr.Name] = attr.Value
      })

      return {
        accessToken: authResult.AuthenticationResult.AccessToken,
        idToken: authResult.AuthenticationResult.IdToken,
        refreshToken: authResult.AuthenticationResult.RefreshToken,
        expiresIn: authResult.AuthenticationResult.ExpiresIn,
        user: {
          userId: userResult.Username,
          email: userAttributes.email,
          username: userAttributes.username
        }
      }
    } catch (error) {
      console.error('Error authenticating user:', error)
      
      if (error.name === 'NotAuthorizedException') {
        throw new Error('Invalid email or password')
      }
      
      if (error.name === 'UserNotFoundException') {
        throw new Error('User not found')
      }
      
      throw new Error(`Authentication failed: ${error.message}`)
    }
  }

  /**
   * Gets user information from Cognito
   * @param {string} accessToken - User's access token
   * @returns {Promise<Object>} - User information
   */
  async getUserFromToken(accessToken) {
    try {
      // For this implementation, we'll extract the username from the token
      // In a production environment, you might want to verify the token first
      const payload = JSON.parse(Buffer.from(accessToken.split('.')[1], 'base64').toString())
      const username = payload.username

      const getUserCommand = new AdminGetUserCommand({
        UserPoolId: this.userPoolId,
        Username: username
      })

      const userResult = await this.client.send(getUserCommand)
      
      const userAttributes = {}
      userResult.UserAttributes.forEach(attr => {
        userAttributes[attr.Name] = attr.Value
      })

      return {
        userId: userResult.Username,
        email: userAttributes.email,
        username: userAttributes.username
      }
    } catch (error) {
      console.error('Error getting user from token:', error)
      throw new Error('Invalid or expired token')
    }
  }
}

module.exports = CognitoService
