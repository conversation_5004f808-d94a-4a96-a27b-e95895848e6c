{"name": "tunami-mvp", "version": "1.0.0", "description": "Tunami AI Music Platform MVP - A dedicated platform for AI-generated music", "private": true, "scripts": {"install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test -- --run", "test:coverage": "npm run test:coverage:backend && npm run test:coverage:frontend", "test:coverage:backend": "cd backend && npm run test:coverage", "test:coverage:frontend": "cd frontend && npm run test:coverage", "build": "npm run build:backend && npm run build:frontend", "build:backend": "sam build", "build:frontend": "cd frontend && npm run build", "dev:backend": "sam local start-api", "dev:frontend": "cd frontend && npm run dev", "deploy:dev": "bash scripts/deploy.sh dev all", "deploy:staging": "bash scripts/deploy.sh staging all", "deploy:prod": "bash scripts/deploy.sh prod all", "deploy:backend:dev": "bash scripts/deploy.sh dev backend", "deploy:frontend:dev": "bash scripts/deploy.sh dev frontend", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "clean": "rm -rf backend/node_modules frontend/node_modules .aws-sam", "setup": "npm run install:all && npm run build", "validate": "npm run lint && npm run test && npm run build"}, "repository": {"type": "git", "url": "https://github.com/your-username/tunami-mvp.git"}, "keywords": ["ai-music", "music-platform", "serverless", "aws", "react", "nodejs", "mvp"], "author": "Tunami Team", "license": "PROPRIETARY", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "workspaces": ["backend", "frontend"], "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "config": {"aws-region": "us-east-1", "node-version": "18"}}