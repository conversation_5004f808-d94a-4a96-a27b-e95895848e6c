import type { HttpResponse, SerdeContext } from "@smithy/types";
/**
 * @internal
 */
export declare const parseJsonBody: (streamBody: any, context: SerdeContext) => any;
/**
 * @internal
 */
export declare const parseJsonErrorBody: (errorBody: any, context: SerdeContext) => Promise<any>;
/**
 * @internal
 */
export declare const loadRestJsonErrorCode: (output: HttpResponse, data: any) => string | undefined;
