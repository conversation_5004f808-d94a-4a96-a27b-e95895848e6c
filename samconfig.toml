version = 0.1

[default]
[default.deploy]
[default.deploy.parameters]
stack_name = "tunami-backend-dev"
s3_bucket = ""  # Will be created automatically
s3_prefix = "tunami-backend"
region = "us-east-1"
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=dev"
confirm_changeset = false
fail_on_empty_changeset = false

[staging]
[staging.deploy]
[staging.deploy.parameters]
stack_name = "tunami-backend-staging"
s3_bucket = ""  # Will be created automatically
s3_prefix = "tunami-backend"
region = "us-east-1"
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=staging"
confirm_changeset = false
fail_on_empty_changeset = false

[production]
[production.deploy]
[production.deploy.parameters]
stack_name = "tunami-backend-prod"
s3_bucket = ""  # Will be created automatically
s3_prefix = "tunami-backend"
region = "us-east-1"
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=prod"
confirm_changeset = true  # Require confirmation for production
fail_on_empty_changeset = false
