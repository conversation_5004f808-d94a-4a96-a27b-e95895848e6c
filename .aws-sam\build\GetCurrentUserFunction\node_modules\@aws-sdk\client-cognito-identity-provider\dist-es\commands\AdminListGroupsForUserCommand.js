import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { AdminListGroupsForUserRequestFilterSensitiveLog, } from "../models/models_0";
import { de_AdminListGroupsForUserCommand, se_AdminListGroupsForUserCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class AdminListGroupsForUserCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSCognitoIdentityProviderService", "AdminListGroupsForUser", {})
    .n("CognitoIdentityProviderClient", "AdminListGroupsForUserCommand")
    .f(AdminListGroupsForUserRequestFilterSensitiveLog, void 0)
    .ser(se_AdminListGroupsForUserCommand)
    .de(de_AdminListGroupsForUserCommand)
    .build() {
}
