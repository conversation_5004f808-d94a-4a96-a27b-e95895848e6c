AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Tunami MVP - User Service Infrastructure

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name

Globals:
  Function:
    Timeout: 30
    Runtime: nodejs18.x
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        USER_POOL_ID: !Ref TunamiUserPool
        USER_POOL_CLIENT_ID: !Ref TunamiUserPoolClient
        USERS_TABLE_NAME: !Ref TunamiUsersTable

Resources:
  # Cognito User Pool
  TunamiUserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: !Sub tunami-users-${Environment}
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false
      UsernameAttributes:
        - email
      AutoVerifiedAttributes:
        - email
      Schema:
        - Name: email
          AttributeDataType: String
          Required: true
          Mutable: true
        - Name: username
          AttributeDataType: String
          Required: true
          Mutable: true

  TunamiUserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      UserPoolId: !Ref TunamiUserPool
      ClientName: !Sub tunami-client-${Environment}
      GenerateSecret: false
      ExplicitAuthFlows:
        - ADMIN_NO_SRP_AUTH
        - USER_PASSWORD_AUTH
      TokenValidityUnits:
        AccessToken: hours
        IdToken: hours
        RefreshToken: days
      AccessTokenValidity: 24
      IdTokenValidity: 24
      RefreshTokenValidity: 30

  # DynamoDB Table
  TunamiUsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub TunamiUsers-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: email
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: EmailIndex
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  # API Gateway
  TunamiApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      Auth:
        DefaultAuthorizer: CognitoAuthorizer
        Authorizers:
          CognitoAuthorizer:
            UserPoolArn: !GetAtt TunamiUserPool.Arn

  # Lambda Functions
  RegisterUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-register-user-${Environment}
      CodeUri: backend/src/
      Handler: handlers/registerUser.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TunamiUsersTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - cognito-idp:AdminCreateUser
                - cognito-idp:AdminSetUserPassword
                - cognito-idp:AdminConfirmSignUp
              Resource: !GetAtt TunamiUserPool.Arn
      Events:
        RegisterUser:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/register
            Method: post
            Auth:
              Authorizer: NONE

  LoginUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-login-user-${Environment}
      CodeUri: backend/src/
      Handler: handlers/loginUser.handler
      Policies:
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - cognito-idp:AdminInitiateAuth
                - cognito-idp:AdminGetUser
              Resource: !GetAtt TunamiUserPool.Arn
      Events:
        LoginUser:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/login
            Method: post
            Auth:
              Authorizer: NONE

  GetCurrentUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-get-current-user-${Environment}
      CodeUri: backend/src/
      Handler: handlers/getCurrentUser.handler
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiUsersTable
      Events:
        GetCurrentUser:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/me
            Method: get

Outputs:
  ApiGatewayUrl:
    Description: API Gateway endpoint URL
    Value: !Sub "https://${TunamiApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
    Export:
      Name: !Sub ${AWS::StackName}-ApiUrl

  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref TunamiUserPool
    Export:
      Name: !Sub ${AWS::StackName}-UserPoolId

  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref TunamiUserPoolClient
    Export:
      Name: !Sub ${AWS::StackName}-UserPoolClientId
