import { Provider, RegionInfoProvider, UrlParser } from "@smithy/types";
interface GetEndpointFromRegionOptions {
    region: Provider<string>;
    tls?: boolean;
    regionInfoProvider: RegionInfoProvider;
    urlParser: UrlParser;
    useDualstackEndpoint: Provider<boolean>;
    useFipsEndpoint: Provider<boolean>;
}
export declare const getEndpointFromRegion: (input: GetEndpointFromRegionOptions) => Promise<import("@smithy/types").Endpoint>;
export {};
