import {
  HttpRequest as __HttpRequest,
  HttpResponse as __HttpResponse,
} from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import {
  CreateTokenCommandInput,
  CreateTokenCommandOutput,
} from "../commands/CreateTokenCommand";
export declare const se_CreateTokenCommand: (
  input: CreateTokenCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const de_CreateTokenCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateTokenCommandOutput>;
