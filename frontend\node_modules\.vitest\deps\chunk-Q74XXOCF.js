var __getOwnPropNames = Object.getOwnPropertyNames;
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};

// vite:cjs-external-facade:react
import * as m from "react";
var require_react = __commonJS({
  "vite:cjs-external-facade:react"(exports, module) {
    module.exports = m;
  }
});

export {
  __commonJS,
  require_react
};
//# sourceMappingURL=chunk-Q74XXOCF.js.map
