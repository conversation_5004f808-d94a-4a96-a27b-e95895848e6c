import { FinalizeHandler, MetadataBearer, Pluggable, RelativeMiddlewareOptions } from "@smithy/types";
/**
 * @internal
 */
export declare const omitRetryHeadersMiddleware: () => <Output extends MetadataBearer = MetadataBearer>(next: FinalizeHandler<any, Output>) => FinalizeHandler<any, Output>;
/**
 * @internal
 */
export declare const omitRetryHeadersMiddlewareOptions: RelativeMiddlewareOptions;
/**
 * @internal
 */
export declare const getOmitRetryHeadersPlugin: (options: unknown) => Pluggable<any, any>;
