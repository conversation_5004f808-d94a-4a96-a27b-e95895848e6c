import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import { DescribeTableInput, DescribeTableOutput } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DescribeTableCommandInput extends DescribeTableInput {}
export interface DescribeTableCommandOutput
  extends DescribeTableOutput,
    __MetadataBearer {}
declare const DescribeTableCommand_base: {
  new (
    input: DescribeTableCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTableCommandInput,
    DescribeTableCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeTableCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTableCommandInput,
    DescribeTableCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeTableCommand extends DescribeTableCommand_base {
  protected static __types: {
    api: {
      input: DescribeTableInput;
      output: DescribeTableOutput;
    };
    sdk: {
      input: DescribeTableCommandInput;
      output: DescribeTableCommandOutput;
    };
  };
}
