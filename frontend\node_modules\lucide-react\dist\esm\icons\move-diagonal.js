/**
 * lucide-react v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const MoveDiagonal = createLucideIcon("MoveDiagonal", [
  ["polyline", { points: "13 5 19 5 19 11", key: "11219e" }],
  ["polyline", { points: "11 19 5 19 5 13", key: "sfq3wq" }],
  ["line", { x1: "19", x2: "5", y1: "5", y2: "19", key: "1x9vlm" }]
]);

export { MoveDiagonal as default };
//# sourceMappingURL=move-diagonal.js.map
