const CognitoService = require('../services/cognitoService')
const DynamoService = require('../services/dynamoService')
const { loginUserSchema, validateRequest } = require('../utils/validation')
const { successResponse, errorResponse, validationErrorResponse } = require('../utils/response')

const cognitoService = new CognitoService()
const dynamoService = new DynamoService()

/**
 * Lambda handler for user login
 * POST /users/login
 */
exports.handler = async (event) => {
  console.log('Login user request:', JSON.stringify(event, null, 2))

  try {
    // Parse request body
    let requestBody
    try {
      requestBody = JSON.parse(event.body || '{}')
    } catch (error) {
      return errorResponse('Invalid JSON in request body', 400, 'INVALID_JSON')
    }

    // Validate request data
    const validation = validateRequest(requestBody, loginUserSchema)
    if (!validation.isValid) {
      return validationErrorResponse(validation.errors)
    }

    const { email, password } = validation.value

    // Authenticate user with Cognito
    let authResult
    try {
      authResult = await cognitoService.authenticateUser(email, password)
    } catch (error) {
      console.error('Authentication failed:', error)
      
      if (error.message.includes('Invalid email or password') || 
          error.message.includes('User not found')) {
        return errorResponse('Invalid email or password', 401, 'INVALID_CREDENTIALS')
      }
      
      return errorResponse('Authentication failed', 500, 'AUTH_ERROR')
    }

    // Get user data from DynamoDB
    let userData
    try {
      userData = await dynamoService.getUserByEmail(email)
      
      if (!userData) {
        console.error('User authenticated in Cognito but not found in DynamoDB:', email)
        return errorResponse('User data not found', 500, 'DATA_INCONSISTENCY')
      }

      // Check if user account is active
      if (!userData.isActive) {
        return errorResponse('User account is deactivated', 403, 'ACCOUNT_DEACTIVATED')
      }

    } catch (error) {
      console.error('Error retrieving user data:', error)
      return errorResponse('Failed to retrieve user data', 500, 'DATABASE_ERROR')
    }

    // Prepare response data
    const responseData = {
      user: {
        userId: userData.userId,
        username: userData.username,
        email: userData.email,
        createdAt: userData.createdAt
      },
      tokens: {
        accessToken: authResult.accessToken,
        idToken: authResult.idToken,
        refreshToken: authResult.refreshToken,
        expiresIn: authResult.expiresIn
      }
    }

    return successResponse(responseData, 200)

  } catch (error) {
    console.error('Unexpected error in loginUser:', error)
    return errorResponse('Internal server error', 500, 'INTERNAL_ERROR')
  }
}
