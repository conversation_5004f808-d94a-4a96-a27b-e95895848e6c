import { Dynamo<PERSON><PERSON>lient } from "@aws-sdk/client-dynamodb";
import { HttpHandlerOptions as __HttpHandlerOptions } from "@smithy/types";
import {
  BatchExecuteStatementCommandInput,
  BatchExecuteStatementCommandOutput,
} from "./commands/BatchExecuteStatementCommand";
import {
  BatchGetCommandInput,
  BatchGetCommandOutput,
} from "./commands/BatchGetCommand";
import {
  BatchWriteCommandInput,
  BatchWriteCommandOutput,
} from "./commands/BatchWriteCommand";
import {
  DeleteCommandInput,
  DeleteCommandOutput,
} from "./commands/DeleteCommand";
import {
  ExecuteStatementCommandInput,
  ExecuteStatementCommandOutput,
} from "./commands/ExecuteStatementCommand";
import {
  ExecuteTransactionCommandInput,
  ExecuteTransactionCommandOutput,
} from "./commands/ExecuteTransactionCommand";
import { GetCommandInput, GetCommandOutput } from "./commands/GetCommand";
import { PutCommandInput, PutCommandOutput } from "./commands/PutCommand";
import { QueryCommandInput, QueryCommandOutput } from "./commands/QueryCommand";
import { ScanCommandInput, ScanCommandOutput } from "./commands/ScanCommand";
import {
  TransactGetCommandInput,
  TransactGetCommandOutput,
} from "./commands/TransactGetCommand";
import {
  TransactWriteCommandInput,
  TransactWriteCommandOutput,
} from "./commands/TransactWriteCommand";
import {
  UpdateCommandInput,
  UpdateCommandOutput,
} from "./commands/UpdateCommand";
import {
  DynamoDBDocumentClient,
  TranslateConfig,
} from "./DynamoDBDocumentClient";
export declare class DynamoDBDocument extends DynamoDBDocumentClient {
  static from(
    client: DynamoDBClient,
    translateConfig?: TranslateConfig
  ): DynamoDBDocument;
  batchExecuteStatement(
    args: BatchExecuteStatementCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<BatchExecuteStatementCommandOutput>;
  batchExecuteStatement(
    args: BatchExecuteStatementCommandInput,
    cb: (err: any, data?: BatchExecuteStatementCommandOutput) => void
  ): void;
  batchExecuteStatement(
    args: BatchExecuteStatementCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: BatchExecuteStatementCommandOutput) => void
  ): void;
  batchGet(
    args: BatchGetCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<BatchGetCommandOutput>;
  batchGet(
    args: BatchGetCommandInput,
    cb: (err: any, data?: BatchGetCommandOutput) => void
  ): void;
  batchGet(
    args: BatchGetCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: BatchGetCommandOutput) => void
  ): void;
  batchWrite(
    args: BatchWriteCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<BatchWriteCommandOutput>;
  batchWrite(
    args: BatchWriteCommandInput,
    cb: (err: any, data?: BatchWriteCommandOutput) => void
  ): void;
  batchWrite(
    args: BatchWriteCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: BatchWriteCommandOutput) => void
  ): void;
  delete(
    args: DeleteCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteCommandOutput>;
  delete(
    args: DeleteCommandInput,
    cb: (err: any, data?: DeleteCommandOutput) => void
  ): void;
  delete(
    args: DeleteCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteCommandOutput) => void
  ): void;
  executeStatement(
    args: ExecuteStatementCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ExecuteStatementCommandOutput>;
  executeStatement(
    args: ExecuteStatementCommandInput,
    cb: (err: any, data?: ExecuteStatementCommandOutput) => void
  ): void;
  executeStatement(
    args: ExecuteStatementCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ExecuteStatementCommandOutput) => void
  ): void;
  executeTransaction(
    args: ExecuteTransactionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ExecuteTransactionCommandOutput>;
  executeTransaction(
    args: ExecuteTransactionCommandInput,
    cb: (err: any, data?: ExecuteTransactionCommandOutput) => void
  ): void;
  executeTransaction(
    args: ExecuteTransactionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ExecuteTransactionCommandOutput) => void
  ): void;
  get(
    args: GetCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetCommandOutput>;
  get(
    args: GetCommandInput,
    cb: (err: any, data?: GetCommandOutput) => void
  ): void;
  get(
    args: GetCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetCommandOutput) => void
  ): void;
  put(
    args: PutCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutCommandOutput>;
  put(
    args: PutCommandInput,
    cb: (err: any, data?: PutCommandOutput) => void
  ): void;
  put(
    args: PutCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutCommandOutput) => void
  ): void;
  query(
    args: QueryCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<QueryCommandOutput>;
  query(
    args: QueryCommandInput,
    cb: (err: any, data?: QueryCommandOutput) => void
  ): void;
  query(
    args: QueryCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: QueryCommandOutput) => void
  ): void;
  scan(
    args: ScanCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ScanCommandOutput>;
  scan(
    args: ScanCommandInput,
    cb: (err: any, data?: ScanCommandOutput) => void
  ): void;
  scan(
    args: ScanCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ScanCommandOutput) => void
  ): void;
  transactGet(
    args: TransactGetCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<TransactGetCommandOutput>;
  transactGet(
    args: TransactGetCommandInput,
    cb: (err: any, data?: TransactGetCommandOutput) => void
  ): void;
  transactGet(
    args: TransactGetCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: TransactGetCommandOutput) => void
  ): void;
  transactWrite(
    args: TransactWriteCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<TransactWriteCommandOutput>;
  transactWrite(
    args: TransactWriteCommandInput,
    cb: (err: any, data?: TransactWriteCommandOutput) => void
  ): void;
  transactWrite(
    args: TransactWriteCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: TransactWriteCommandOutput) => void
  ): void;
  update(
    args: UpdateCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateCommandOutput>;
  update(
    args: UpdateCommandInput,
    cb: (err: any, data?: UpdateCommandOutput) => void
  ): void;
  update(
    args: UpdateCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateCommandOutput) => void
  ): void;
}
