const { handler } = require('../handlers/uploadTrack')

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3')
jest.mock('@aws-sdk/s3-request-presigner')

const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3')
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner')

describe('uploadTrack Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock environment variables
    process.env.AUDIO_BUCKET_NAME = 'test-bucket'
    process.env.AWS_REGION = 'us-east-1'
    
    // Mock getSignedUrl
    getSignedUrl.mockResolvedValue('https://test-bucket.s3.amazonaws.com/presigned-url')
  })

  const createMockEvent = (body, userId = 'test-user-123') => ({
    body: JSON.stringify(body),
    requestContext: {
      authorizer: {
        claims: {
          sub: userId
        }
      }
    }
  })

  describe('Success Cases', () => {
    test('should generate presigned URL for valid MP3 upload', async () => {
      const event = createMockEvent({
        fileName: 'test-track.mp3',
        fileSize: 5000000, // 5MB
        contentType: 'audio/mpeg'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data).toHaveProperty('uploadUrl')
      expect(response.data).toHaveProperty('fileKey')
      expect(response.data).toHaveProperty('s3Url')
      expect(response.data).toHaveProperty('expiresIn', 900)
      
      // Verify S3 client was called correctly
      expect(getSignedUrl).toHaveBeenCalledWith(
        expect.any(S3Client),
        expect.any(PutObjectCommand),
        { expiresIn: 900 }
      )
    })

    test('should generate unique file keys for different uploads', async () => {
      const event1 = createMockEvent({
        fileName: 'track1.mp3',
        fileSize: 1000000,
        contentType: 'audio/mpeg'
      })

      const event2 = createMockEvent({
        fileName: 'track2.mp3',
        fileSize: 2000000,
        contentType: 'audio/mpeg'
      })

      const result1 = await handler(event1)
      const result2 = await handler(event2)

      const response1 = JSON.parse(result1.body)
      const response2 = JSON.parse(result2.body)

      expect(response1.data.fileKey).not.toBe(response2.data.fileKey)
      expect(response1.data.fileKey).toMatch(/^tracks\/test-user-123\/.*\.mp3$/)
      expect(response2.data.fileKey).toMatch(/^tracks\/test-user-123\/.*\.mp3$/)
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing fileName', async () => {
      const event = createMockEvent({
        fileSize: 1000000,
        contentType: 'audio/mpeg'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject file size over 50MB', async () => {
      const event = createMockEvent({
        fileName: 'large-track.mp3',
        fileSize: 60 * 1024 * 1024, // 60MB
        contentType: 'audio/mpeg'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject invalid content type', async () => {
      const event = createMockEvent({
        fileName: 'track.txt',
        fileSize: 1000000,
        contentType: 'text/plain'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('Authentication Errors', () => {
    test('should reject unauthenticated requests', async () => {
      const event = {
        body: JSON.stringify({
          fileName: 'test-track.mp3',
          fileSize: 1000000,
          contentType: 'audio/mpeg'
        }),
        requestContext: {}
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(401)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('UNAUTHORIZED')
    })
  })

  describe('Error Handling', () => {
    test('should handle S3 service errors', async () => {
      getSignedUrl.mockRejectedValue(new Error('S3 service error'))

      const event = createMockEvent({
        fileName: 'test-track.mp3',
        fileSize: 1000000,
        contentType: 'audio/mpeg'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('UPLOAD_ERROR')
    })

    test('should handle malformed JSON body', async () => {
      const event = {
        body: 'invalid json',
        requestContext: {
          authorizer: {
            claims: {
              sub: 'test-user-123'
            }
          }
        }
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
    })
  })
})
