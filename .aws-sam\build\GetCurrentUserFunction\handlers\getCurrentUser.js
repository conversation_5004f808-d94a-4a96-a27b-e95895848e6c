const DynamoService = require('../services/dynamoService')
const { successResponse, errorResponse } = require('../utils/response')

const dynamoService = new DynamoService()

/**
 * Lambda handler for getting current user information
 * GET /users/me
 * Requires Cognito authorization
 */
exports.handler = async (event) => {
  console.log('Get current user request:', JSON.stringify(event, null, 2))

  try {
    // Extract user information from the Cognito authorizer context
    const requestContext = event.requestContext
    const authorizer = requestContext.authorizer
    
    if (!authorizer || !authorizer.claims) {
      return errorResponse('Authorization information not found', 401, 'UNAUTHORIZED')
    }

    // Get user ID from Cognito claims
    const cognitoUsername = authorizer.claims.sub || authorizer.claims['cognito:username']
    const email = authorizer.claims.email
    
    if (!email) {
      return errorResponse('User email not found in token', 401, 'INVALID_TOKEN')
    }

    // Get user data from DynamoDB using email
    let userD<PERSON>
    try {
      userData = await dynamoService.getUserByEmail(email)
      
      if (!userData) {
        console.error('User authenticated but not found in DynamoDB:', email)
        return errorResponse('User data not found', 404, 'USER_NOT_FOUND')
      }

      // Check if user account is active
      if (!userData.isActive) {
        return errorResponse('User account is deactivated', 403, 'ACCOUNT_DEACTIVATED')
      }

    } catch (error) {
      console.error('Error retrieving user data:', error)
      return errorResponse('Failed to retrieve user data', 500, 'DATABASE_ERROR')
    }

    // Prepare response data (excluding sensitive information)
    const responseData = {
      userId: userData.userId,
      username: userData.username,
      email: userData.email,
      createdAt: userData.createdAt,
      updatedAt: userData.updatedAt,
      isActive: userData.isActive
    }

    return successResponse(responseData, 200)

  } catch (error) {
    console.error('Unexpected error in getCurrentUser:', error)
    return errorResponse('Internal server error', 500, 'INTERNAL_ERROR')
  }
}
