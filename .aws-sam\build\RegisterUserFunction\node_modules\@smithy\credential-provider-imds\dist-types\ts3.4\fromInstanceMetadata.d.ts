import { Provider } from "@smithy/types";
import { RemoteProviderInit } from "./remoteProvider/RemoteProviderInit";
import { InstanceMetadataCredentials } from "./types";
/**
 * @internal
 *
 * Creates a credential provider that will source credentials from the EC2
 * Instance Metadata Service
 */
export declare const fromInstanceMetadata: (init?: RemoteProviderInit) => Provider<InstanceMetadataCredentials>;
