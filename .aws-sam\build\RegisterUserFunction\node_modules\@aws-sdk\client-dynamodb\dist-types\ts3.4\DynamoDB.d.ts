import { HttpHandlerOptions as __HttpHandlerOptions } from "@smithy/types";
import {
  BatchExecuteStatementCommandInput,
  BatchExecuteStatementCommandOutput,
} from "./commands/BatchExecuteStatementCommand";
import {
  BatchGetItemCommandInput,
  BatchGetItemCommandOutput,
} from "./commands/BatchGetItemCommand";
import {
  BatchWriteItemCommandInput,
  BatchWriteItemCommandOutput,
} from "./commands/BatchWriteItemCommand";
import {
  CreateBackupCommandInput,
  CreateBackupCommandOutput,
} from "./commands/CreateBackupCommand";
import {
  CreateGlobalTableCommandInput,
  CreateGlobalTableCommandOutput,
} from "./commands/CreateGlobalTableCommand";
import {
  CreateTableCommandInput,
  CreateTableCommandOutput,
} from "./commands/CreateTableCommand";
import {
  DeleteBackupCommandInput,
  DeleteBackupCommandOutput,
} from "./commands/DeleteBackupCommand";
import {
  DeleteItemCommandInput,
  DeleteItemCommandOutput,
} from "./commands/DeleteItemCommand";
import {
  DeleteResourcePolicyCommandInput,
  DeleteResourcePolicyCommandOutput,
} from "./commands/DeleteResourcePolicyCommand";
import {
  DeleteTableCommandInput,
  DeleteTableCommandOutput,
} from "./commands/DeleteTableCommand";
import {
  DescribeBackupCommandInput,
  DescribeBackupCommandOutput,
} from "./commands/DescribeBackupCommand";
import {
  DescribeContinuousBackupsCommandInput,
  DescribeContinuousBackupsCommandOutput,
} from "./commands/DescribeContinuousBackupsCommand";
import {
  DescribeContributorInsightsCommandInput,
  DescribeContributorInsightsCommandOutput,
} from "./commands/DescribeContributorInsightsCommand";
import {
  DescribeEndpointsCommandInput,
  DescribeEndpointsCommandOutput,
} from "./commands/DescribeEndpointsCommand";
import {
  DescribeExportCommandInput,
  DescribeExportCommandOutput,
} from "./commands/DescribeExportCommand";
import {
  DescribeGlobalTableCommandInput,
  DescribeGlobalTableCommandOutput,
} from "./commands/DescribeGlobalTableCommand";
import {
  DescribeGlobalTableSettingsCommandInput,
  DescribeGlobalTableSettingsCommandOutput,
} from "./commands/DescribeGlobalTableSettingsCommand";
import {
  DescribeImportCommandInput,
  DescribeImportCommandOutput,
} from "./commands/DescribeImportCommand";
import {
  DescribeKinesisStreamingDestinationCommandInput,
  DescribeKinesisStreamingDestinationCommandOutput,
} from "./commands/DescribeKinesisStreamingDestinationCommand";
import {
  DescribeLimitsCommandInput,
  DescribeLimitsCommandOutput,
} from "./commands/DescribeLimitsCommand";
import {
  DescribeTableCommandInput,
  DescribeTableCommandOutput,
} from "./commands/DescribeTableCommand";
import {
  DescribeTableReplicaAutoScalingCommandInput,
  DescribeTableReplicaAutoScalingCommandOutput,
} from "./commands/DescribeTableReplicaAutoScalingCommand";
import {
  DescribeTimeToLiveCommandInput,
  DescribeTimeToLiveCommandOutput,
} from "./commands/DescribeTimeToLiveCommand";
import {
  DisableKinesisStreamingDestinationCommandInput,
  DisableKinesisStreamingDestinationCommandOutput,
} from "./commands/DisableKinesisStreamingDestinationCommand";
import {
  EnableKinesisStreamingDestinationCommandInput,
  EnableKinesisStreamingDestinationCommandOutput,
} from "./commands/EnableKinesisStreamingDestinationCommand";
import {
  ExecuteStatementCommandInput,
  ExecuteStatementCommandOutput,
} from "./commands/ExecuteStatementCommand";
import {
  ExecuteTransactionCommandInput,
  ExecuteTransactionCommandOutput,
} from "./commands/ExecuteTransactionCommand";
import {
  ExportTableToPointInTimeCommandInput,
  ExportTableToPointInTimeCommandOutput,
} from "./commands/ExportTableToPointInTimeCommand";
import {
  GetItemCommandInput,
  GetItemCommandOutput,
} from "./commands/GetItemCommand";
import {
  GetResourcePolicyCommandInput,
  GetResourcePolicyCommandOutput,
} from "./commands/GetResourcePolicyCommand";
import {
  ImportTableCommandInput,
  ImportTableCommandOutput,
} from "./commands/ImportTableCommand";
import {
  ListBackupsCommandInput,
  ListBackupsCommandOutput,
} from "./commands/ListBackupsCommand";
import {
  ListContributorInsightsCommandInput,
  ListContributorInsightsCommandOutput,
} from "./commands/ListContributorInsightsCommand";
import {
  ListExportsCommandInput,
  ListExportsCommandOutput,
} from "./commands/ListExportsCommand";
import {
  ListGlobalTablesCommandInput,
  ListGlobalTablesCommandOutput,
} from "./commands/ListGlobalTablesCommand";
import {
  ListImportsCommandInput,
  ListImportsCommandOutput,
} from "./commands/ListImportsCommand";
import {
  ListTablesCommandInput,
  ListTablesCommandOutput,
} from "./commands/ListTablesCommand";
import {
  ListTagsOfResourceCommandInput,
  ListTagsOfResourceCommandOutput,
} from "./commands/ListTagsOfResourceCommand";
import {
  PutItemCommandInput,
  PutItemCommandOutput,
} from "./commands/PutItemCommand";
import {
  PutResourcePolicyCommandInput,
  PutResourcePolicyCommandOutput,
} from "./commands/PutResourcePolicyCommand";
import { QueryCommandInput, QueryCommandOutput } from "./commands/QueryCommand";
import {
  RestoreTableFromBackupCommandInput,
  RestoreTableFromBackupCommandOutput,
} from "./commands/RestoreTableFromBackupCommand";
import {
  RestoreTableToPointInTimeCommandInput,
  RestoreTableToPointInTimeCommandOutput,
} from "./commands/RestoreTableToPointInTimeCommand";
import { ScanCommandInput, ScanCommandOutput } from "./commands/ScanCommand";
import {
  TagResourceCommandInput,
  TagResourceCommandOutput,
} from "./commands/TagResourceCommand";
import {
  TransactGetItemsCommandInput,
  TransactGetItemsCommandOutput,
} from "./commands/TransactGetItemsCommand";
import {
  TransactWriteItemsCommandInput,
  TransactWriteItemsCommandOutput,
} from "./commands/TransactWriteItemsCommand";
import {
  UntagResourceCommandInput,
  UntagResourceCommandOutput,
} from "./commands/UntagResourceCommand";
import {
  UpdateContinuousBackupsCommandInput,
  UpdateContinuousBackupsCommandOutput,
} from "./commands/UpdateContinuousBackupsCommand";
import {
  UpdateContributorInsightsCommandInput,
  UpdateContributorInsightsCommandOutput,
} from "./commands/UpdateContributorInsightsCommand";
import {
  UpdateGlobalTableCommandInput,
  UpdateGlobalTableCommandOutput,
} from "./commands/UpdateGlobalTableCommand";
import {
  UpdateGlobalTableSettingsCommandInput,
  UpdateGlobalTableSettingsCommandOutput,
} from "./commands/UpdateGlobalTableSettingsCommand";
import {
  UpdateItemCommandInput,
  UpdateItemCommandOutput,
} from "./commands/UpdateItemCommand";
import {
  UpdateKinesisStreamingDestinationCommandInput,
  UpdateKinesisStreamingDestinationCommandOutput,
} from "./commands/UpdateKinesisStreamingDestinationCommand";
import {
  UpdateTableCommandInput,
  UpdateTableCommandOutput,
} from "./commands/UpdateTableCommand";
import {
  UpdateTableReplicaAutoScalingCommandInput,
  UpdateTableReplicaAutoScalingCommandOutput,
} from "./commands/UpdateTableReplicaAutoScalingCommand";
import {
  UpdateTimeToLiveCommandInput,
  UpdateTimeToLiveCommandOutput,
} from "./commands/UpdateTimeToLiveCommand";
import { DynamoDBClient } from "./DynamoDBClient";
export interface DynamoDB {
  batchExecuteStatement(
    args: BatchExecuteStatementCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<BatchExecuteStatementCommandOutput>;
  batchExecuteStatement(
    args: BatchExecuteStatementCommandInput,
    cb: (err: any, data?: BatchExecuteStatementCommandOutput) => void
  ): void;
  batchExecuteStatement(
    args: BatchExecuteStatementCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: BatchExecuteStatementCommandOutput) => void
  ): void;
  batchGetItem(
    args: BatchGetItemCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<BatchGetItemCommandOutput>;
  batchGetItem(
    args: BatchGetItemCommandInput,
    cb: (err: any, data?: BatchGetItemCommandOutput) => void
  ): void;
  batchGetItem(
    args: BatchGetItemCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: BatchGetItemCommandOutput) => void
  ): void;
  batchWriteItem(
    args: BatchWriteItemCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<BatchWriteItemCommandOutput>;
  batchWriteItem(
    args: BatchWriteItemCommandInput,
    cb: (err: any, data?: BatchWriteItemCommandOutput) => void
  ): void;
  batchWriteItem(
    args: BatchWriteItemCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: BatchWriteItemCommandOutput) => void
  ): void;
  createBackup(
    args: CreateBackupCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateBackupCommandOutput>;
  createBackup(
    args: CreateBackupCommandInput,
    cb: (err: any, data?: CreateBackupCommandOutput) => void
  ): void;
  createBackup(
    args: CreateBackupCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateBackupCommandOutput) => void
  ): void;
  createGlobalTable(
    args: CreateGlobalTableCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateGlobalTableCommandOutput>;
  createGlobalTable(
    args: CreateGlobalTableCommandInput,
    cb: (err: any, data?: CreateGlobalTableCommandOutput) => void
  ): void;
  createGlobalTable(
    args: CreateGlobalTableCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateGlobalTableCommandOutput) => void
  ): void;
  createTable(
    args: CreateTableCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateTableCommandOutput>;
  createTable(
    args: CreateTableCommandInput,
    cb: (err: any, data?: CreateTableCommandOutput) => void
  ): void;
  createTable(
    args: CreateTableCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateTableCommandOutput) => void
  ): void;
  deleteBackup(
    args: DeleteBackupCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBackupCommandOutput>;
  deleteBackup(
    args: DeleteBackupCommandInput,
    cb: (err: any, data?: DeleteBackupCommandOutput) => void
  ): void;
  deleteBackup(
    args: DeleteBackupCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBackupCommandOutput) => void
  ): void;
  deleteItem(
    args: DeleteItemCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteItemCommandOutput>;
  deleteItem(
    args: DeleteItemCommandInput,
    cb: (err: any, data?: DeleteItemCommandOutput) => void
  ): void;
  deleteItem(
    args: DeleteItemCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteItemCommandOutput) => void
  ): void;
  deleteResourcePolicy(
    args: DeleteResourcePolicyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteResourcePolicyCommandOutput>;
  deleteResourcePolicy(
    args: DeleteResourcePolicyCommandInput,
    cb: (err: any, data?: DeleteResourcePolicyCommandOutput) => void
  ): void;
  deleteResourcePolicy(
    args: DeleteResourcePolicyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteResourcePolicyCommandOutput) => void
  ): void;
  deleteTable(
    args: DeleteTableCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteTableCommandOutput>;
  deleteTable(
    args: DeleteTableCommandInput,
    cb: (err: any, data?: DeleteTableCommandOutput) => void
  ): void;
  deleteTable(
    args: DeleteTableCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteTableCommandOutput) => void
  ): void;
  describeBackup(
    args: DescribeBackupCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeBackupCommandOutput>;
  describeBackup(
    args: DescribeBackupCommandInput,
    cb: (err: any, data?: DescribeBackupCommandOutput) => void
  ): void;
  describeBackup(
    args: DescribeBackupCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeBackupCommandOutput) => void
  ): void;
  describeContinuousBackups(
    args: DescribeContinuousBackupsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeContinuousBackupsCommandOutput>;
  describeContinuousBackups(
    args: DescribeContinuousBackupsCommandInput,
    cb: (err: any, data?: DescribeContinuousBackupsCommandOutput) => void
  ): void;
  describeContinuousBackups(
    args: DescribeContinuousBackupsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeContinuousBackupsCommandOutput) => void
  ): void;
  describeContributorInsights(
    args: DescribeContributorInsightsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeContributorInsightsCommandOutput>;
  describeContributorInsights(
    args: DescribeContributorInsightsCommandInput,
    cb: (err: any, data?: DescribeContributorInsightsCommandOutput) => void
  ): void;
  describeContributorInsights(
    args: DescribeContributorInsightsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeContributorInsightsCommandOutput) => void
  ): void;
  describeEndpoints(): Promise<DescribeEndpointsCommandOutput>;
  describeEndpoints(
    args: DescribeEndpointsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeEndpointsCommandOutput>;
  describeEndpoints(
    args: DescribeEndpointsCommandInput,
    cb: (err: any, data?: DescribeEndpointsCommandOutput) => void
  ): void;
  describeEndpoints(
    args: DescribeEndpointsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeEndpointsCommandOutput) => void
  ): void;
  describeExport(
    args: DescribeExportCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeExportCommandOutput>;
  describeExport(
    args: DescribeExportCommandInput,
    cb: (err: any, data?: DescribeExportCommandOutput) => void
  ): void;
  describeExport(
    args: DescribeExportCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeExportCommandOutput) => void
  ): void;
  describeGlobalTable(
    args: DescribeGlobalTableCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeGlobalTableCommandOutput>;
  describeGlobalTable(
    args: DescribeGlobalTableCommandInput,
    cb: (err: any, data?: DescribeGlobalTableCommandOutput) => void
  ): void;
  describeGlobalTable(
    args: DescribeGlobalTableCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeGlobalTableCommandOutput) => void
  ): void;
  describeGlobalTableSettings(
    args: DescribeGlobalTableSettingsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeGlobalTableSettingsCommandOutput>;
  describeGlobalTableSettings(
    args: DescribeGlobalTableSettingsCommandInput,
    cb: (err: any, data?: DescribeGlobalTableSettingsCommandOutput) => void
  ): void;
  describeGlobalTableSettings(
    args: DescribeGlobalTableSettingsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeGlobalTableSettingsCommandOutput) => void
  ): void;
  describeImport(
    args: DescribeImportCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeImportCommandOutput>;
  describeImport(
    args: DescribeImportCommandInput,
    cb: (err: any, data?: DescribeImportCommandOutput) => void
  ): void;
  describeImport(
    args: DescribeImportCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeImportCommandOutput) => void
  ): void;
  describeKinesisStreamingDestination(
    args: DescribeKinesisStreamingDestinationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeKinesisStreamingDestinationCommandOutput>;
  describeKinesisStreamingDestination(
    args: DescribeKinesisStreamingDestinationCommandInput,
    cb: (
      err: any,
      data?: DescribeKinesisStreamingDestinationCommandOutput
    ) => void
  ): void;
  describeKinesisStreamingDestination(
    args: DescribeKinesisStreamingDestinationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: DescribeKinesisStreamingDestinationCommandOutput
    ) => void
  ): void;
  describeLimits(): Promise<DescribeLimitsCommandOutput>;
  describeLimits(
    args: DescribeLimitsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeLimitsCommandOutput>;
  describeLimits(
    args: DescribeLimitsCommandInput,
    cb: (err: any, data?: DescribeLimitsCommandOutput) => void
  ): void;
  describeLimits(
    args: DescribeLimitsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeLimitsCommandOutput) => void
  ): void;
  describeTable(
    args: DescribeTableCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeTableCommandOutput>;
  describeTable(
    args: DescribeTableCommandInput,
    cb: (err: any, data?: DescribeTableCommandOutput) => void
  ): void;
  describeTable(
    args: DescribeTableCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeTableCommandOutput) => void
  ): void;
  describeTableReplicaAutoScaling(
    args: DescribeTableReplicaAutoScalingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeTableReplicaAutoScalingCommandOutput>;
  describeTableReplicaAutoScaling(
    args: DescribeTableReplicaAutoScalingCommandInput,
    cb: (err: any, data?: DescribeTableReplicaAutoScalingCommandOutput) => void
  ): void;
  describeTableReplicaAutoScaling(
    args: DescribeTableReplicaAutoScalingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeTableReplicaAutoScalingCommandOutput) => void
  ): void;
  describeTimeToLive(
    args: DescribeTimeToLiveCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeTimeToLiveCommandOutput>;
  describeTimeToLive(
    args: DescribeTimeToLiveCommandInput,
    cb: (err: any, data?: DescribeTimeToLiveCommandOutput) => void
  ): void;
  describeTimeToLive(
    args: DescribeTimeToLiveCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeTimeToLiveCommandOutput) => void
  ): void;
  disableKinesisStreamingDestination(
    args: DisableKinesisStreamingDestinationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DisableKinesisStreamingDestinationCommandOutput>;
  disableKinesisStreamingDestination(
    args: DisableKinesisStreamingDestinationCommandInput,
    cb: (
      err: any,
      data?: DisableKinesisStreamingDestinationCommandOutput
    ) => void
  ): void;
  disableKinesisStreamingDestination(
    args: DisableKinesisStreamingDestinationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: DisableKinesisStreamingDestinationCommandOutput
    ) => void
  ): void;
  enableKinesisStreamingDestination(
    args: EnableKinesisStreamingDestinationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<EnableKinesisStreamingDestinationCommandOutput>;
  enableKinesisStreamingDestination(
    args: EnableKinesisStreamingDestinationCommandInput,
    cb: (
      err: any,
      data?: EnableKinesisStreamingDestinationCommandOutput
    ) => void
  ): void;
  enableKinesisStreamingDestination(
    args: EnableKinesisStreamingDestinationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: EnableKinesisStreamingDestinationCommandOutput
    ) => void
  ): void;
  executeStatement(
    args: ExecuteStatementCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ExecuteStatementCommandOutput>;
  executeStatement(
    args: ExecuteStatementCommandInput,
    cb: (err: any, data?: ExecuteStatementCommandOutput) => void
  ): void;
  executeStatement(
    args: ExecuteStatementCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ExecuteStatementCommandOutput) => void
  ): void;
  executeTransaction(
    args: ExecuteTransactionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ExecuteTransactionCommandOutput>;
  executeTransaction(
    args: ExecuteTransactionCommandInput,
    cb: (err: any, data?: ExecuteTransactionCommandOutput) => void
  ): void;
  executeTransaction(
    args: ExecuteTransactionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ExecuteTransactionCommandOutput) => void
  ): void;
  exportTableToPointInTime(
    args: ExportTableToPointInTimeCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ExportTableToPointInTimeCommandOutput>;
  exportTableToPointInTime(
    args: ExportTableToPointInTimeCommandInput,
    cb: (err: any, data?: ExportTableToPointInTimeCommandOutput) => void
  ): void;
  exportTableToPointInTime(
    args: ExportTableToPointInTimeCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ExportTableToPointInTimeCommandOutput) => void
  ): void;
  getItem(
    args: GetItemCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetItemCommandOutput>;
  getItem(
    args: GetItemCommandInput,
    cb: (err: any, data?: GetItemCommandOutput) => void
  ): void;
  getItem(
    args: GetItemCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetItemCommandOutput) => void
  ): void;
  getResourcePolicy(
    args: GetResourcePolicyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetResourcePolicyCommandOutput>;
  getResourcePolicy(
    args: GetResourcePolicyCommandInput,
    cb: (err: any, data?: GetResourcePolicyCommandOutput) => void
  ): void;
  getResourcePolicy(
    args: GetResourcePolicyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetResourcePolicyCommandOutput) => void
  ): void;
  importTable(
    args: ImportTableCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ImportTableCommandOutput>;
  importTable(
    args: ImportTableCommandInput,
    cb: (err: any, data?: ImportTableCommandOutput) => void
  ): void;
  importTable(
    args: ImportTableCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ImportTableCommandOutput) => void
  ): void;
  listBackups(): Promise<ListBackupsCommandOutput>;
  listBackups(
    args: ListBackupsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListBackupsCommandOutput>;
  listBackups(
    args: ListBackupsCommandInput,
    cb: (err: any, data?: ListBackupsCommandOutput) => void
  ): void;
  listBackups(
    args: ListBackupsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListBackupsCommandOutput) => void
  ): void;
  listContributorInsights(): Promise<ListContributorInsightsCommandOutput>;
  listContributorInsights(
    args: ListContributorInsightsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListContributorInsightsCommandOutput>;
  listContributorInsights(
    args: ListContributorInsightsCommandInput,
    cb: (err: any, data?: ListContributorInsightsCommandOutput) => void
  ): void;
  listContributorInsights(
    args: ListContributorInsightsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListContributorInsightsCommandOutput) => void
  ): void;
  listExports(): Promise<ListExportsCommandOutput>;
  listExports(
    args: ListExportsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListExportsCommandOutput>;
  listExports(
    args: ListExportsCommandInput,
    cb: (err: any, data?: ListExportsCommandOutput) => void
  ): void;
  listExports(
    args: ListExportsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListExportsCommandOutput) => void
  ): void;
  listGlobalTables(): Promise<ListGlobalTablesCommandOutput>;
  listGlobalTables(
    args: ListGlobalTablesCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListGlobalTablesCommandOutput>;
  listGlobalTables(
    args: ListGlobalTablesCommandInput,
    cb: (err: any, data?: ListGlobalTablesCommandOutput) => void
  ): void;
  listGlobalTables(
    args: ListGlobalTablesCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListGlobalTablesCommandOutput) => void
  ): void;
  listImports(): Promise<ListImportsCommandOutput>;
  listImports(
    args: ListImportsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListImportsCommandOutput>;
  listImports(
    args: ListImportsCommandInput,
    cb: (err: any, data?: ListImportsCommandOutput) => void
  ): void;
  listImports(
    args: ListImportsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListImportsCommandOutput) => void
  ): void;
  listTables(): Promise<ListTablesCommandOutput>;
  listTables(
    args: ListTablesCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListTablesCommandOutput>;
  listTables(
    args: ListTablesCommandInput,
    cb: (err: any, data?: ListTablesCommandOutput) => void
  ): void;
  listTables(
    args: ListTablesCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListTablesCommandOutput) => void
  ): void;
  listTagsOfResource(
    args: ListTagsOfResourceCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListTagsOfResourceCommandOutput>;
  listTagsOfResource(
    args: ListTagsOfResourceCommandInput,
    cb: (err: any, data?: ListTagsOfResourceCommandOutput) => void
  ): void;
  listTagsOfResource(
    args: ListTagsOfResourceCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListTagsOfResourceCommandOutput) => void
  ): void;
  putItem(
    args: PutItemCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutItemCommandOutput>;
  putItem(
    args: PutItemCommandInput,
    cb: (err: any, data?: PutItemCommandOutput) => void
  ): void;
  putItem(
    args: PutItemCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutItemCommandOutput) => void
  ): void;
  putResourcePolicy(
    args: PutResourcePolicyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutResourcePolicyCommandOutput>;
  putResourcePolicy(
    args: PutResourcePolicyCommandInput,
    cb: (err: any, data?: PutResourcePolicyCommandOutput) => void
  ): void;
  putResourcePolicy(
    args: PutResourcePolicyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutResourcePolicyCommandOutput) => void
  ): void;
  query(
    args: QueryCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<QueryCommandOutput>;
  query(
    args: QueryCommandInput,
    cb: (err: any, data?: QueryCommandOutput) => void
  ): void;
  query(
    args: QueryCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: QueryCommandOutput) => void
  ): void;
  restoreTableFromBackup(
    args: RestoreTableFromBackupCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<RestoreTableFromBackupCommandOutput>;
  restoreTableFromBackup(
    args: RestoreTableFromBackupCommandInput,
    cb: (err: any, data?: RestoreTableFromBackupCommandOutput) => void
  ): void;
  restoreTableFromBackup(
    args: RestoreTableFromBackupCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: RestoreTableFromBackupCommandOutput) => void
  ): void;
  restoreTableToPointInTime(
    args: RestoreTableToPointInTimeCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<RestoreTableToPointInTimeCommandOutput>;
  restoreTableToPointInTime(
    args: RestoreTableToPointInTimeCommandInput,
    cb: (err: any, data?: RestoreTableToPointInTimeCommandOutput) => void
  ): void;
  restoreTableToPointInTime(
    args: RestoreTableToPointInTimeCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: RestoreTableToPointInTimeCommandOutput) => void
  ): void;
  scan(
    args: ScanCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ScanCommandOutput>;
  scan(
    args: ScanCommandInput,
    cb: (err: any, data?: ScanCommandOutput) => void
  ): void;
  scan(
    args: ScanCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ScanCommandOutput) => void
  ): void;
  tagResource(
    args: TagResourceCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<TagResourceCommandOutput>;
  tagResource(
    args: TagResourceCommandInput,
    cb: (err: any, data?: TagResourceCommandOutput) => void
  ): void;
  tagResource(
    args: TagResourceCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: TagResourceCommandOutput) => void
  ): void;
  transactGetItems(
    args: TransactGetItemsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<TransactGetItemsCommandOutput>;
  transactGetItems(
    args: TransactGetItemsCommandInput,
    cb: (err: any, data?: TransactGetItemsCommandOutput) => void
  ): void;
  transactGetItems(
    args: TransactGetItemsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: TransactGetItemsCommandOutput) => void
  ): void;
  transactWriteItems(
    args: TransactWriteItemsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<TransactWriteItemsCommandOutput>;
  transactWriteItems(
    args: TransactWriteItemsCommandInput,
    cb: (err: any, data?: TransactWriteItemsCommandOutput) => void
  ): void;
  transactWriteItems(
    args: TransactWriteItemsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: TransactWriteItemsCommandOutput) => void
  ): void;
  untagResource(
    args: UntagResourceCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UntagResourceCommandOutput>;
  untagResource(
    args: UntagResourceCommandInput,
    cb: (err: any, data?: UntagResourceCommandOutput) => void
  ): void;
  untagResource(
    args: UntagResourceCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UntagResourceCommandOutput) => void
  ): void;
  updateContinuousBackups(
    args: UpdateContinuousBackupsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateContinuousBackupsCommandOutput>;
  updateContinuousBackups(
    args: UpdateContinuousBackupsCommandInput,
    cb: (err: any, data?: UpdateContinuousBackupsCommandOutput) => void
  ): void;
  updateContinuousBackups(
    args: UpdateContinuousBackupsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateContinuousBackupsCommandOutput) => void
  ): void;
  updateContributorInsights(
    args: UpdateContributorInsightsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateContributorInsightsCommandOutput>;
  updateContributorInsights(
    args: UpdateContributorInsightsCommandInput,
    cb: (err: any, data?: UpdateContributorInsightsCommandOutput) => void
  ): void;
  updateContributorInsights(
    args: UpdateContributorInsightsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateContributorInsightsCommandOutput) => void
  ): void;
  updateGlobalTable(
    args: UpdateGlobalTableCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateGlobalTableCommandOutput>;
  updateGlobalTable(
    args: UpdateGlobalTableCommandInput,
    cb: (err: any, data?: UpdateGlobalTableCommandOutput) => void
  ): void;
  updateGlobalTable(
    args: UpdateGlobalTableCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateGlobalTableCommandOutput) => void
  ): void;
  updateGlobalTableSettings(
    args: UpdateGlobalTableSettingsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateGlobalTableSettingsCommandOutput>;
  updateGlobalTableSettings(
    args: UpdateGlobalTableSettingsCommandInput,
    cb: (err: any, data?: UpdateGlobalTableSettingsCommandOutput) => void
  ): void;
  updateGlobalTableSettings(
    args: UpdateGlobalTableSettingsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateGlobalTableSettingsCommandOutput) => void
  ): void;
  updateItem(
    args: UpdateItemCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateItemCommandOutput>;
  updateItem(
    args: UpdateItemCommandInput,
    cb: (err: any, data?: UpdateItemCommandOutput) => void
  ): void;
  updateItem(
    args: UpdateItemCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateItemCommandOutput) => void
  ): void;
  updateKinesisStreamingDestination(
    args: UpdateKinesisStreamingDestinationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateKinesisStreamingDestinationCommandOutput>;
  updateKinesisStreamingDestination(
    args: UpdateKinesisStreamingDestinationCommandInput,
    cb: (
      err: any,
      data?: UpdateKinesisStreamingDestinationCommandOutput
    ) => void
  ): void;
  updateKinesisStreamingDestination(
    args: UpdateKinesisStreamingDestinationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: UpdateKinesisStreamingDestinationCommandOutput
    ) => void
  ): void;
  updateTable(
    args: UpdateTableCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateTableCommandOutput>;
  updateTable(
    args: UpdateTableCommandInput,
    cb: (err: any, data?: UpdateTableCommandOutput) => void
  ): void;
  updateTable(
    args: UpdateTableCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateTableCommandOutput) => void
  ): void;
  updateTableReplicaAutoScaling(
    args: UpdateTableReplicaAutoScalingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateTableReplicaAutoScalingCommandOutput>;
  updateTableReplicaAutoScaling(
    args: UpdateTableReplicaAutoScalingCommandInput,
    cb: (err: any, data?: UpdateTableReplicaAutoScalingCommandOutput) => void
  ): void;
  updateTableReplicaAutoScaling(
    args: UpdateTableReplicaAutoScalingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateTableReplicaAutoScalingCommandOutput) => void
  ): void;
  updateTimeToLive(
    args: UpdateTimeToLiveCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateTimeToLiveCommandOutput>;
  updateTimeToLive(
    args: UpdateTimeToLiveCommandInput,
    cb: (err: any, data?: UpdateTimeToLiveCommandOutput) => void
  ): void;
  updateTimeToLive(
    args: UpdateTimeToLiveCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateTimeToLiveCommandOutput) => void
  ): void;
}
export declare class DynamoDB extends DynamoDBClient implements DynamoDB {}
