import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { AdminUpdateUserAttributesRequestFilterSensitiveLog, } from "../models/models_0";
import { de_AdminUpdateUserAttributesCommand, se_AdminUpdateUserAttributesCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class AdminUpdateUserAttributesCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSCognitoIdentityProviderService", "AdminUpdateUserAttributes", {})
    .n("CognitoIdentityProviderClient", "AdminUpdateUserAttributesCommand")
    .f(AdminUpdateUserAttributesRequestFilterSensitiveLog, void 0)
    .ser(se_AdminUpdateUserAttributesCommand)
    .de(de_AdminUpdateUserAttributesCommand)
    .build() {
}
