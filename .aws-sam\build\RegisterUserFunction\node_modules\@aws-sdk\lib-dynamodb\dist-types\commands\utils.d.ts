import { marshallOptions, unmarshallOptions } from "@aws-sdk/util-dynamodb";
/**
 * @internal
 */
export type KeyNodeSelf = null;
/**
 * @internal
 */
export declare const SELF: KeyNodeSelf;
/**
 * @internal
 */
export type KeyNodeChildren = Record<string, any>;
/**
 * @internal
 */
export declare const ALL_VALUES: KeyNodeChildren;
/**
 * @internal
 */
export declare const ALL_MEMBERS: KeyNodeChildren;
/**
 * @internal
 */
export type KeyNodes = KeyNodeSelf | KeyNodeChildren;
/**
 * @internal
 */
export declare const marshallInput: (obj: any, keyNodes: KeyNodeChildren, options?: marshallOptions) => any;
/**
 * @internal
 */
export declare const unmarshallOutput: (obj: any, keyNodes: KeyNodeChildren, options?: unmarshallOptions) => any;
