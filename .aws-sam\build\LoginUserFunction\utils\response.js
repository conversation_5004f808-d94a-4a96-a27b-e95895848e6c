/**
 * Utility functions for creating standardized API responses
 */

const createResponse = (statusCode, body, headers = {}) => {
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
      'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
      ...headers
    },
    body: JSON.stringify(body)
  }
}

const successResponse = (data, statusCode = 200) => {
  return createResponse(statusCode, {
    success: true,
    data
  })
}

const errorResponse = (message, statusCode = 400, errorCode = null) => {
  return createResponse(statusCode, {
    success: false,
    error: {
      message,
      code: errorCode
    }
  })
}

const validationErrorResponse = (errors) => {
  return createResponse(400, {
    success: false,
    error: {
      message: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors
    }
  })
}

module.exports = {
  createResponse,
  successResponse,
  errorResponse,
  validationErrorResponse
}
