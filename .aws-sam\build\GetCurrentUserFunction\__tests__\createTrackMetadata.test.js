const { handler } = require('../handlers/createTrackMetadata')

// Mock AWS SDK
jest.mock('@aws-sdk/client-dynamodb')
jest.mock('@aws-sdk/util-dynamodb')

const { DynamoDBClient, PutItemCommand } = require('@aws-sdk/client-dynamodb')
const { marshall } = require('@aws-sdk/util-dynamodb')

describe('createTrackMetadata Lambda Handler', () => {
  let mockSend

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock environment variables
    process.env.TRACKS_TABLE_NAME = 'test-tracks-table'
    process.env.AWS_REGION = 'us-east-1'
    
    // Mock DynamoDB client
    mockSend = jest.fn()
    DynamoDBClient.prototype.send = mockSend
    
    // Mock marshall function
    marshall.mockImplementation((obj) => obj)
  })

  const createMockEvent = (body, userId = 'test-user-123') => ({
    body: JSON.stringify(body),
    requestContext: {
      authorizer: {
        claims: {
          sub: userId
        }
      }
    }
  })

  describe('Success Cases', () => {
    test('should create track metadata successfully', async () => {
      mockSend.mockResolvedValue({})

      const event = createMockEvent({
        title: 'Test Track',
        genre: 'Electronic',
        description: 'A test track',
        aiToolsUsed: ['AIVA', 'Soundraw'],
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/user123/track.mp3',
        isPublic: true,
        tags: ['ambient', 'chill']
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data).toHaveProperty('trackId')
      expect(response.data).toHaveProperty('creatorId', 'test-user-123')
      expect(response.data).toHaveProperty('title', 'Test Track')
      expect(response.data).toHaveProperty('genre', 'Electronic')
      
      // Verify DynamoDB was called
      expect(mockSend).toHaveBeenCalledWith(expect.any(PutItemCommand))
    })

    test('should handle minimal required fields', async () => {
      mockSend.mockResolvedValue({})

      const event = createMockEvent({
        title: 'Minimal Track',
        genre: 'Jazz',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/minimal.mp3',
        fileKey: 'tracks/user123/minimal.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.title).toBe('Minimal Track')
      expect(response.data.genre).toBe('Jazz')
    })

    test('should default isPublic to true when not specified', async () => {
      mockSend.mockResolvedValue({})

      const event = createMockEvent({
        title: 'Public Track',
        genre: 'Pop',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/public.mp3',
        fileKey: 'tracks/user123/public.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.isPublic).toBe(true)
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing title', async () => {
      const event = createMockEvent({
        genre: 'Electronic',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/user123/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject missing genre', async () => {
      const event = createMockEvent({
        title: 'Test Track',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/user123/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject invalid audioFileUrl', async () => {
      const event = createMockEvent({
        title: 'Test Track',
        genre: 'Electronic',
        audioFileUrl: 'not-a-valid-url',
        fileKey: 'tracks/user123/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject title that is too long', async () => {
      const event = createMockEvent({
        title: 'A'.repeat(201), // 201 characters
        genre: 'Electronic',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/user123/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('Authentication Errors', () => {
    test('should reject unauthenticated requests', async () => {
      const event = {
        body: JSON.stringify({
          title: 'Test Track',
          genre: 'Electronic',
          audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
          fileKey: 'tracks/user123/track.mp3'
        }),
        requestContext: {}
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(401)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('UNAUTHORIZED')
    })
  })

  describe('Database Errors', () => {
    test('should handle DynamoDB errors', async () => {
      mockSend.mockRejectedValue(new Error('DynamoDB error'))

      const event = createMockEvent({
        title: 'Test Track',
        genre: 'Electronic',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/user123/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('METADATA_ERROR')
    })

    test('should handle duplicate track condition', async () => {
      const conditionalError = new Error('Conditional check failed')
      conditionalError.name = 'ConditionalCheckFailedException'
      mockSend.mockRejectedValue(conditionalError)

      const event = createMockEvent({
        title: 'Duplicate Track',
        genre: 'Electronic',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/user123/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(409)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('DUPLICATE_TRACK')
    })
  })
})
